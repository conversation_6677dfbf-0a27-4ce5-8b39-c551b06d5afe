- 潜在问题和改进建议：

- 字体文件路径是硬编码的（"simhei.ttf"），建议改为可配置项
- 缺少错误重试机制，当截图失败时直接跳过
- 检测结果没有保存或记录功能
- 注释中提到的视频合成功能尚未实现
- 窗口大小是固定的，可以考虑添加缩放功能
- 性能优化点：

- 可以添加帧率控制
- 考虑添加检测结果的缓存机制
- 可以实现异步处理提高性能
- 添加 GPU 内存管理
- 建议添加的功能：

- 检测结果的保存和导出
- 视频录制功能
- 检测参数的实时调整
- 多模型切换支持
- 检测结果的统计分析

- 建议添加防抖处理，避免窗口轻微抖动导致频繁重启录制
- 可以考虑添加位置更新的回调函数，用于通知外部窗口位置变化


请参考@__init__.py 中的find_ocr_by_color方法，新增一个find_color_by_ocr_targets方法，该方法主要有以下功能：
1、区域截图
2、目标颜色，如rgb元组、单点找色、多点找色
3、相似度
4、

# 快速冒烟测试 
python scripts/run_tests.py --type smoke 
 
# 完整测试套件 
python scripts/run_tests.py --type all --generate-report 
 
# 真实游戏测试 
python scripts/run_tests.py --type integration --real-game 
 
# 测试演示 
python examples/test_integration_demo.py