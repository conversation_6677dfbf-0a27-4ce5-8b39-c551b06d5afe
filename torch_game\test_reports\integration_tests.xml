<?xml version="1.0" encoding="utf-8"?><testsuites name="pytest tests"><testsuite name="pytest" errors="0" failures="0" skipped="1" tests="8" time="133.290" timestamp="2025-06-26T09:45:57.662870+08:00" hostname="DESKTOP-BKSHHBC"><testcase classname="torch_game.tests.integration.test_end_to_end.TestEndToEnd" name="test_mock_environment_pipeline" time="0.265" /><testcase classname="torch_game.tests.integration.test_end_to_end.TestEndToEnd" name="test_short_training_pipeline" time="26.663" /><testcase classname="torch_game.tests.integration.test_end_to_end.TestEndToEnd" name="test_model_save_load" time="0.048" /><testcase classname="torch_game.tests.integration.test_end_to_end.TestEndToEnd" name="test_evaluation_pipeline" time="4.128" /><testcase classname="torch_game.tests.integration.test_end_to_end.TestEndToEnd" name="test_memory_usage" time="0.056" /><testcase classname="torch_game.tests.integration.test_end_to_end.TestEndToEnd" name="test_error_handling" time="0.038" /><testcase classname="torch_game.tests.integration.test_end_to_end.TestEndToEnd" name="test_complete_workflow" time="30.873" /><testcase classname="torch_game.tests.integration.test_real_game_integration.TestRealGameIntegration" name="test_game_window_detection" time="1.718"><skipped type="pytest.skip" message="真实游戏环境不可用">E:\xy2_yolo_auxiliary\torch_game\tests\conftest.py:218: 真实游戏环境不可用</skipped></testcase></testsuite></testsuites>