<?xml version="1.0" encoding="utf-8"?><testsuites name="pytest tests"><testsuite name="pytest" errors="0" failures="1" skipped="0" tests="11" time="10.401" timestamp="2025-06-26T09:44:11.427755+08:00" hostname="DESKTOP-BKSHHBC"><testcase classname="torch_game.tests.performance.test_memory_profiling.TestMemoryProfiling" name="test_environment_memory_lifecycle" time="4.195" /><testcase classname="torch_game.tests.performance.test_memory_profiling.TestMemoryProfiling" name="test_agent_memory_usage" time="0.149" /><testcase classname="torch_game.tests.performance.test_memory_profiling.TestMemoryProfiling" name="test_training_memory_growth" time="0.076"><failure message="TypeError: Trainer.__init__() got an unexpected keyword argument 'n_episodes'">tests\performance\test_memory_profiling.py:220: in test_training_memory_growth
    self.trainer = Trainer(
E   TypeError: Trainer.__init__() got an unexpected keyword argument 'n_episodes'</failure></testcase><testcase classname="torch_game.tests.performance.test_memory_profiling.TestMemoryProfiling" name="test_batch_size_memory_scaling" time="0.296" /><testcase classname="torch_game.tests.performance.test_memory_profiling.TestMemoryProfiling" name="test_long_running_memory_stability" time="0.750" /><testcase classname="torch_game.tests.performance.test_performance_benchmarks.TestPerformanceBenchmarks" name="test_environment_creation_speed" time="0.073" /><testcase classname="torch_game.tests.performance.test_performance_benchmarks.TestPerformanceBenchmarks" name="test_agent_inference_speed" time="0.108" /><testcase classname="torch_game.tests.performance.test_performance_benchmarks.TestPerformanceBenchmarks" name="test_training_step_speed" time="0.107" /><testcase classname="torch_game.tests.performance.test_performance_benchmarks.TestPerformanceBenchmarks" name="test_memory_usage_baseline" time="2.138" /><testcase classname="torch_game.tests.performance.test_performance_benchmarks.TestPerformanceBenchmarks" name="test_scalability_benchmark" time="0.173" /><testcase classname="torch_game.tests.performance.test_performance_benchmarks.TestPerformanceBenchmarks" name="test_gpu_vs_cpu_performance" time="1.989" /></testsuite></testsuites>