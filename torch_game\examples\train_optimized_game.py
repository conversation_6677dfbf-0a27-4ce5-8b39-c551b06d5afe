"""
使用优化环境训练三消游戏智能体
集成了s4_三消.py的优化状态管理逻辑
"""

import sys
import os

# 添加项目路径 - 从E:\xy2_yolo_auxiliary根目录执行
current_dir = os.path.dirname(os.path.abspath(__file__))
torch_game_dir = os.path.dirname(current_dir)
project_root = os.path.dirname(torch_game_dir)

sys.path.append(project_root)  # 添加根目录到路径
sys.path.append(torch_game_dir)  # 添加torch_game目录到路径

import torch
import numpy as np
import time
from torch_game.core.env import OptimizedMatch3Env
from torch_game.core.agents import PPOAgent
from torch_game.core.utils.trainer import Trainer
from torch_game.core.utils.config import Config

# 导入游戏助手 - 从根目录导入
from s4_三消 import TripleEliminationGameAssistant
from assist import AssistBasicToolkit


def create_game_assistant():
    """创建游戏助手实例"""
    try:
        print("正在初始化游戏助手...")
        assist = AssistBasicToolkit()
        
        # 注册并绑定窗口操作对象
        hwnd = assist.register_and_bind_window_objects('最强祖师')
        print(f"找到游戏窗口，句柄: {hwnd}")
        
        # 加载YOLO模型 - 使用相对于根目录的路径
        model_path = "models/sanxiao/best.pt"
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"模型文件不存在: {model_path}")
        
        assist.detection.load_model(model_path)
        print(f"YOLO模型加载成功: {model_path}")
        
        # 创建游戏助手
        assistant = TripleEliminationGameAssistant.created(hwnd, assist)
        print("游戏助手创建成功")
        
        return assistant
        
    except Exception as e:
        print(f"创建游戏助手失败: {e}")
        return None


def main():
    """主训练函数"""
    print("🎮 优化环境强化学习训练启动...")
    print("=" * 60)

    # 创建训练配置
    config = Config({
        'env': {
            'max_steps': 150,
            'n_colors': 19,
            'storage_capacity': 7
        },
        'agent': {
            'lr': 3e-4,
            'gamma': 0.99,
            'epsilon': 0.2,  # PPO裁剪参数
            'entropy_coef': 0.01,
            'value_coef': 0.5,
            'hidden_dim': 256
        },
        'training': {
            'total_episodes': 50,  # 先用较少回合测试
            'update_interval': 20,
            'batch_size': 8,
            'n_epochs': 4,
            'log_interval': 1,
            'eval_interval': 5,
            'save_interval': 10
        },
        'logging': {
            'log_dir': 'logs',
            'model_dir': 'models/real_game'
        }
    })

    print(f"📋 训练配置:")
    print(f"   🎮 最大步数: {config.get('env.max_steps')}")
    print(f"   🎨 方块颜色数: {config.get('env.n_colors')}")
    print(f"   📦 预存区容量: {config.get('env.storage_capacity')}")
    print(f"   🎯 训练回合数: {config.get('training.total_episodes')}")
    print(f"   🔄 更新间隔: {config.get('training.update_interval')}")

    # 调试配置值
    print(f"\n🔍 调试配置值:")
    print(f"   env.max_steps = {config.get('env.max_steps')} (类型: {type(config.get('env.max_steps'))})")
    print(f"   env.n_colors = {config.get('env.n_colors')} (类型: {type(config.get('env.n_colors'))})")
    print(f"   env.storage_capacity = {config.get('env.storage_capacity')} (类型: {type(config.get('env.storage_capacity'))})")
    print("-" * 60)
    
    # 创建游戏助手
    game_assistant = create_game_assistant()
    if game_assistant is None:
        print("无法创建游戏助手，退出训练")
        return
    
    try:
        # 创建环境
        print("\n🏗️ 正在创建训练环境...")
        env = OptimizedMatch3Env(
            game_assistant=game_assistant,
            max_steps=config.get('env.max_steps'),
            n_colors=config.get('env.n_colors'),
            storage_capacity=config.get('env.storage_capacity')
        )
        print(f"✅ 环境创建成功")
        print(f"   📊 观察空间: {env.observation_space.shape}")
        print(f"   🎯 动作空间: {env.action_space.n}")

        # 创建智能体
        print("\n🧠 正在创建PPO智能体...")
        agent = PPOAgent(
            state_dim=env.observation_space.shape[0],
            action_dim=env.action_space.n,
            lr=config.get('agent.lr'),
            gamma=config.get('agent.gamma'),
            epsilon=config.get('agent.epsilon'),
            entropy_coef=config.get('agent.entropy_coef'),
            value_coef=config.get('agent.value_coef'),
            hidden_dim=config.get('agent.hidden_dim')
        )
        print(f"✅ 智能体创建成功")
        print(f"   🔧 设备: {agent.device}")
        print(f"   📐 隐藏层维度: {config.get('agent.hidden_dim')}")

        # 创建训练器
        print("\n🎯 正在创建训练器...")
        trainer = Trainer(agent, env, config)
        print(f"✅ 训练器创建成功")

        # 开始训练
        print("\n🚀 开始强化学习训练...")
        print("=" * 60)
        start_time = time.time()

        trainer.train()

        training_time = time.time() - start_time
        print("\n" + "=" * 60)
        print(f"🎉 训练完成！总耗时: {training_time:.2f}秒")
        print(f"📊 完成回合数: {len(trainer.episode_rewards)}")
        if trainer.episode_rewards:
            print(f"🎯 平均奖励: {np.mean(trainer.episode_rewards):.4f}")
            print(f"🏆 最佳奖励: {np.max(trainer.episode_rewards):.4f}")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n训练被用户中断")
    except Exception as e:
        print(f"训练过程中出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理资源
        try:
            if 'env' in locals():
                env.close()
            if game_assistant is not None:
                if hasattr(game_assistant, 'assist') and hasattr(game_assistant.assist, 'close'):
                    game_assistant.assist.close()
        except Exception as e:
            print(f"清理资源时出错: {e}")


if __name__ == "__main__":
    main()
