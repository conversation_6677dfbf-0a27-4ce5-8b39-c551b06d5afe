import unittest
import torch
import numpy as np

from torch_game.core.agents import PPOAgent

class TestPPOAgent(unittest.TestCase):
    """测试PPOAgent类"""
    
    def setUp(self):
        """在每个测试方法之前设置智能体"""
        self.state_dim = 10
        self.action_dim = 4
        self.hidden_dims = (64, 32)
        self.lr = 3e-4
        self.gamma = 0.99
        self.gae_lambda = 0.95
        self.clip_epsilon = 0.2
        
        self.agent = PPOAgent(
            state_dim=self.state_dim,
            action_dim=self.action_dim,
            hidden_dim=self.hidden_dims[0],  # 使用第一个hidden_dim值
            lr=self.lr,
            gamma=self.gamma,
            epsilon=self.clip_epsilon  # PPOAgent使用epsilon参数名
        )
        
        # 创建一些示例数据
        self.state = np.random.rand(self.state_dim).astype(np.float32)
        self.states = np.random.rand(32, self.state_dim).astype(np.float32)
        self.actions = np.random.randint(0, self.action_dim, size=32)
        self.rewards = np.random.rand(32).astype(np.float32)
        self.next_states = np.random.rand(32, self.state_dim).astype(np.float32)
        self.dones = np.random.randint(0, 2, size=32).astype(bool)
    
    def test_init(self):
        """测试智能体初始化"""
        # 检查智能体属性
        self.assertEqual(self.agent.state_dim, self.state_dim)
        self.assertEqual(self.agent.action_dim, self.action_dim)
        self.assertEqual(self.agent.gamma, self.gamma)
        self.assertEqual(self.agent.epsilon, self.clip_epsilon)  # PPOAgent使用epsilon属性名

        # 检查网络
        self.assertIsNotNone(self.agent.ac)  # PPOAgent使用ac属性名

        # 检查优化器
        self.assertIsNotNone(self.agent.optimizer)  # PPOAgent使用单一optimizer
    
    def test_select_action(self):
        """测试选择动作"""
        # 设置训练模式以获取log_prob
        self.agent.train()

        # 选择动作
        action, log_prob = self.agent.select_action(self.state)

        # 检查动作是否在有效范围内
        self.assertGreaterEqual(action, 0)
        self.assertLess(action, self.action_dim)

        # 检查数据类型
        self.assertIsInstance(action, int)
        self.assertIsInstance(log_prob, float)

        # 测试确定性动作选择
        self.agent.eval()
        deterministic_action = self.agent.select_action(self.state, deterministic=True)
        self.assertIsInstance(deterministic_action, int)
        self.assertGreaterEqual(deterministic_action, 0)
        self.assertLess(deterministic_action, self.action_dim)
    
    def test_update(self):
        """测试更新"""
        # 创建PPOBuffer格式的批次数据
        batch = {
            'states': self.states,
            'actions': self.actions,
            'log_probs': np.random.randn(32).astype(np.float32),
            'advantages': np.random.randn(32).astype(np.float32),
            'returns': self.rewards + np.random.randn(32).astype(np.float32)
        }

        # 更新智能体
        update_info = self.agent.update(batch)

        # 检查更新信息
        self.assertIn('policy_loss', update_info)
        self.assertIn('value_loss', update_info)
        self.assertIn('entropy', update_info)

        # 检查数据类型
        self.assertIsInstance(update_info['policy_loss'], float)
        self.assertIsInstance(update_info['value_loss'], float)
        self.assertIsInstance(update_info['entropy'], float)
    
    def test_compute_returns(self):
        """测试PPOBuffer的回报计算"""
        from torch_game.core.utils.buffer import PPOBuffer

        # 创建PPOBuffer
        buffer = PPOBuffer(
            state_dim=self.state_dim,
            action_dim=self.action_dim,
            capacity=4,
            gamma=0.99,
            lam=0.95
        )

        # 添加一些示例数据
        rewards = [1.0, 2.0, 3.0, 4.0]
        values = [0.5, 1.5, 2.5, 3.5]

        for i in range(4):
            state = np.random.rand(self.state_dim).astype(np.float32)
            action = np.random.randint(0, self.action_dim)
            reward = rewards[i]
            value = values[i]
            log_prob = -1.0
            done = (i == 3)  # 最后一个episode结束

            buffer.store(state, action, reward, value, log_prob, done)

        # 完成轨迹计算
        buffer.finish_path(0)  # next_value = 0 因为episode结束

        # 获取数据
        data = buffer.get()

        # 检查回报形状
        self.assertEqual(data['returns'].shape[0], 4)

        # 检查回报计算结果（应该是合理的数值）
        returns = data['returns']
        self.assertTrue(np.all(np.isfinite(returns)), "回报值应该是有限的")

        # 检查最后一个回报（应该接近最后一个奖励，因为next_value=0）
        self.assertGreater(returns[-1], 0, "最后一个回报应该大于0")
    
    def test_compute_advantages(self):
        """测试PPOBuffer的优势计算"""
        from torch_game.core.utils.buffer import PPOBuffer

        # 创建PPOBuffer
        buffer = PPOBuffer(
            state_dim=self.state_dim,
            action_dim=self.action_dim,
            capacity=4,
            gamma=0.99,
            lam=0.95
        )

        # 添加一些示例数据
        for i in range(4):
            state = np.random.rand(self.state_dim).astype(np.float32)
            action = np.random.randint(0, self.action_dim)
            reward = float(i + 1)  # 奖励为1, 2, 3, 4
            value = float(i + 0.5)  # 价值为0.5, 1.5, 2.5, 3.5
            log_prob = -1.0
            done = (i == 3)

            buffer.store(state, action, reward, value, log_prob, done)

        # 完成轨迹计算
        buffer.finish_path(0)

        # 获取数据
        data = buffer.get()

        # 检查优势形状
        self.assertEqual(data['advantages'].shape[0], 4)

        # 检查优势计算结果（应该是合理的数值）
        advantages = data['advantages']
        self.assertTrue(np.all(np.isfinite(advantages)), "优势值应该是有限的")

        # 检查优势标准化（均值应该接近0）
        self.assertAlmostEqual(np.mean(advantages), 0.0, places=5)
    
    def test_save_load(self):
        """测试保存和加载模型"""
        # 保存模型
        save_path = "test_model.pt"
        self.agent.save(save_path)
        
        # 创建一个新的智能体
        new_agent = PPOAgent(
            state_dim=self.state_dim,
            action_dim=self.action_dim,
            hidden_dim=self.hidden_dims[0]  # 使用第一个hidden_dim值
        )
        
        # 加载模型
        new_agent.load(save_path)
        
        # 检查两个智能体的参数是否相同
        for param1, param2 in zip(self.agent.ac.parameters(), new_agent.ac.parameters()):
            self.assertTrue(torch.allclose(param1, param2))
        
        # 删除测试文件
        import os
        if os.path.exists(save_path):
            os.remove(save_path)

if __name__ == '__main__':
    unittest.main()