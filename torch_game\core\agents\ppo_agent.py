"""
PPO (Proximal Policy Optimization) 智能体实现
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.distributions import Categorical

from .base_agent import BaseAgent

class ActorCritic(nn.Module):
    """Actor-Critic网络"""
    
    def __init__(self, state_dim, action_dim, hidden_dim=256):
        """
        初始化Actor-Critic网络
        
        Args:
            state_dim: 状态空间维度
            action_dim: 动作空间维度
            hidden_dim: 隐藏层维度
        """
        super().__init__()
        
        # 特征提取器
        self.feature_extractor = nn.Sequential(
            nn.Linear(state_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU()
        )
        
        # Actor网络（策略网络）
        self.actor = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, action_dim),
            nn.Softmax(dim=-1)
        )
        
        # Critic网络（价值网络）
        self.critic = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 1)
        )
        
    def forward(self, state):
        """
        前向传播
        
        Args:
            state: 输入状态
            
        Returns:
            action_probs: 动作概率分布
            state_value: 状态价值
        """
        features = self.feature_extractor(state)
        action_probs = self.actor(features)
        state_value = self.critic(features)
        return action_probs, state_value

class PPOAgent(BaseAgent):
    """PPO智能体"""
    
    def __init__(self, state_dim, action_dim, 
                 lr=3e-4, gamma=0.99, epsilon=0.2, 
                 value_coef=0.5, entropy_coef=0.01,
                 hidden_dim=256, device="cuda" if torch.cuda.is_available() else "cpu"):
        """
        初始化PPO智能体
        
        Args:
            state_dim: 状态空间维度
            action_dim: 动作空间维度
            lr: 学习率
            gamma: 折扣因子
            epsilon: PPO裁剪参数
            value_coef: 价值损失系数
            entropy_coef: 熵正则化系数
            hidden_dim: 隐藏层维度
            device: 运行设备
        """
        super().__init__(state_dim, action_dim, device)
        
        # 创建Actor-Critic网络
        self.ac = ActorCritic(state_dim, action_dim, hidden_dim).to(device)
        self.optimizer = optim.Adam(self.ac.parameters(), lr=lr)
        
        # PPO超参数
        self.gamma = gamma
        self.epsilon = epsilon
        self.value_coef = value_coef
        self.entropy_coef = entropy_coef
        
    def select_action(self, state, deterministic=False):
        """
        根据状态选择动作
        
        Args:
            state: 当前状态
            deterministic: 是否使用确定性策略
            
        Returns:
            action: 选择的动作
            action_log_prob: 动作的对数概率（仅在训练模式下返回）
        """
        # 确保状态是torch tensor且在正确的设备上
        if isinstance(state, np.ndarray):
            state = torch.FloatTensor(state).to(self.device)
        
        with torch.no_grad():
            action_probs, _ = self.ac(state)
            
            if deterministic:
                action = torch.argmax(action_probs).item()
                return action
            
            # 创建分类分布并采样
            dist = Categorical(action_probs)
            action = dist.sample()
            
            if self.training:
                action_log_prob = dist.log_prob(action)
                return action.item(), action_log_prob.item()
            
            return action.item()
    
    def update(self, batch):
        """
        使用一批数据更新智能体
        
        Args:
            batch: 包含transitions的批次数据，包括：
                  states: [batch_size, state_dim]
                  actions: [batch_size]
                  old_log_probs: [batch_size]
                  returns: [batch_size]
                  advantages: [batch_size]
            
        Returns:
            info: 包含损失等信息的字典
        """
        states = torch.FloatTensor(batch['states']).to(self.device)
        actions = torch.LongTensor(batch['actions']).to(self.device)
        old_log_probs = torch.FloatTensor(batch['log_probs']).to(self.device)
        returns = torch.FloatTensor(batch['returns']).to(self.device)
        advantages = torch.FloatTensor(batch['advantages']).to(self.device)
        
        # 计算新的动作概率和状态价值
        action_probs, state_values = self.ac(states)
        dist = Categorical(action_probs)
        new_log_probs = dist.log_prob(actions)
        entropy = dist.entropy().mean()
        
        # 计算策略比率
        ratio = torch.exp(new_log_probs - old_log_probs)
        
        # 计算PPO目标
        surr1 = ratio * advantages
        surr2 = torch.clamp(ratio, 1 - self.epsilon, 1 + self.epsilon) * advantages
        policy_loss = -torch.min(surr1, surr2).mean()
        
        # 计算价值损失
        value_loss = 0.5 * (returns - state_values.squeeze()).pow(2).mean()
        
        # 总损失
        total_loss = (policy_loss + 
                     self.value_coef * value_loss - 
                     self.entropy_coef * entropy)
        
        # 更新网络
        self.optimizer.zero_grad()
        total_loss.backward()
        self.optimizer.step()
        
        return {
            'total_loss': total_loss.item(),
            'policy_loss': policy_loss.item(),
            'value_loss': value_loss.item(),
            'entropy': entropy.item()
        }
    
    def save(self, path):
        """
        保存模型到指定路径
        
        Args:
            path: 保存路径
        """
        torch.save({
            'ac_state_dict': self.ac.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'episode': self.episode,
            'current_step': self.current_step
        }, path)
    
    def load(self, path):
        """
        从指定路径加载模型
        
        Args:
            path: 模型路径
        """
        checkpoint = torch.load(path, map_location=self.device)
        self.ac.load_state_dict(checkpoint['ac_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.episode = checkpoint['episode']
        self.current_step = checkpoint['current_step']