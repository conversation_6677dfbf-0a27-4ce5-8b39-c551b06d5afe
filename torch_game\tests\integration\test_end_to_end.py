"""
端到端集成测试
测试完整的训练和推理流程
"""

import pytest
import os
import sys
import time
import tempfile
import shutil
import logging
from typing import Dict, Any, Optional
import numpy as np
import torch

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from torch_game.core.env import OptimizedMatch3Env
from torch_game.core.agents import PPOAgent
from torch_game.core.utils.trainer import Trainer
from torch_game.tests.utils import (
    TestGameAssistant, TestConfig, create_test_config, create_test_env, 
    create_test_agent, create_test_trainer, MockGameAssistant,
    validate_training_results, validate_performance_metrics,
    setup_test_logging, measure_execution_time, cleanup_temp_files
)


class TestEndToEnd:
    """端到端测试类"""
    
    @classmethod
    def setup_class(cls):
        """类级别的设置"""
        setup_test_logging()
        cls.logger = logging.getLogger(__name__)
        cls.temp_dir = tempfile.mkdtemp()
        cls.config = create_test_config({
            'training.total_episodes': 3,  # 测试时使用很少的回合
            'training.update_interval': 10,  # 确保大于batch_size
            'training.batch_size': 4,       # 使用较小的batch_size
            'env.max_steps': 10
        })
    
    @classmethod
    def teardown_class(cls):
        """类级别的清理"""
        cleanup_temp_files(cls.temp_dir)
    
    def setup_method(self):
        """每个测试方法前的设置"""
        self.env = None
        self.agent = None
        self.trainer = None
    
    def teardown_method(self):
        """每个测试方法后的清理"""
        if self.env:
            self.env.close()
        if self.trainer and hasattr(self.trainer, 'writer'):
            self.trainer.writer.close()
    
    def test_mock_environment_pipeline(self):
        """测试使用模拟环境的完整流程"""
        self.logger.info("开始测试模拟环境完整流程...")
        
        # 创建模拟环境
        mock_assistant = MockGameAssistant()
        self.env = create_test_env(game_assistant=mock_assistant, config=self.config)
        
        # 创建智能体
        self.agent = create_test_agent(env=self.env, config=self.config)
        
        # 测试环境重置
        observation = self.env.reset()
        assert observation is not None, "环境重置失败"
        
        # 测试智能体动作选择
        action, log_prob = self.agent.select_action(observation.reshape(-1))
        assert isinstance(action, (int, np.integer)), "动作类型错误"
        
        # 测试环境步进
        next_obs, reward, done, info = self.env.step(action)
        assert next_obs is not None, "环境步进失败"
        assert isinstance(reward, (int, float)), "奖励类型错误"
        assert isinstance(done, bool), "done类型错误"
        
        self.logger.info("模拟环境完整流程测试成功")
    
    @measure_execution_time
    def test_short_training_pipeline(self):
        """测试短期训练流程"""
        self.logger.info("开始测试短期训练流程...")
        
        # 创建训练器
        self.trainer = create_test_trainer(config=self.config)
        self.env = self.trainer.env
        self.agent = self.trainer.agent
        
        # 记录初始性能
        initial_weights = {
            name: param.clone().detach() 
            for name, param in self.agent.ac.named_parameters()
        }
        
        # 执行短期训练
        start_time = time.time()
        self.trainer.train()
        training_time = time.time() - start_time
        
        # 验证训练结果
        assert len(self.trainer.episode_rewards) > 0, "没有记录到回合奖励"
        
        # 检查网络参数是否更新
        params_updated = False
        for name, param in self.agent.ac.named_parameters():
            if not torch.equal(param, initial_weights[name]):
                params_updated = True
                break
        
        assert params_updated, "网络参数未更新"
        
        # 验证性能指标
        metrics = {
            'training_time': training_time,
            'episodes_completed': len(self.trainer.episode_rewards),
            'avg_reward': np.mean(self.trainer.episode_rewards)
        }
        
        is_valid, errors = validate_performance_metrics(
            metrics, 
            thresholds={
                'training_time': (0.0, 60.0),  # 训练时间不超过1分钟
                'episodes_completed': (1, 10),  # 完成1-10个回合
                'avg_reward': (-100.0, 100.0)   # 平均奖励在合理范围内
            }
        )
        
        if not is_valid:
            self.logger.warning(f"性能指标验证失败: {errors}")
        
        self.logger.info(f"短期训练完成，指标: {metrics}")
        
        return metrics
    
    def test_model_save_load(self):
        """测试模型保存和加载"""
        self.logger.info("开始测试模型保存和加载...")
        
        # 创建智能体
        self.env = create_test_env(config=self.config)
        self.agent = create_test_agent(env=self.env, config=self.config)
        
        # 保存模型
        model_path = os.path.join(self.temp_dir, 'test_model.pth')
        self.agent.save(model_path)
        assert os.path.exists(model_path), "模型文件未保存"
        
        # 记录原始参数
        original_params = {
            name: param.clone().detach() 
            for name, param in self.agent.ac.named_parameters()
        }
        
        # 修改参数
        for param in self.agent.ac.parameters():
            param.data.fill_(0.5)
        
        # 加载模型
        self.agent.load(model_path)
        
        # 验证参数恢复
        for name, param in self.agent.ac.named_parameters():
            assert torch.allclose(param, original_params[name], atol=1e-6), f"参数 {name} 未正确恢复"
        
        self.logger.info("模型保存和加载测试成功")
    
    def test_evaluation_pipeline(self):
        """测试评估流程"""
        self.logger.info("开始测试评估流程...")
        
        # 创建训练器
        self.trainer = create_test_trainer(config=self.config)
        
        # 执行评估
        eval_results = self.trainer.evaluate(n_episodes=2)
        
        # 验证评估结果
        is_valid, errors = validate_training_results(
            eval_results,
            expected_keys=['avg_reward', 'avg_length', 'total_reward', 'episodes'],
            min_episodes=1
        )
        
        assert is_valid, f"评估结果验证失败: {errors}"
        
        self.logger.info(f"评估完成，结果: {eval_results}")
        
        return eval_results
    
    @measure_execution_time
    def test_memory_usage(self):
        """测试内存使用情况"""
        self.logger.info("开始测试内存使用情况...")
        
        import psutil
        import gc
        
        # 记录初始内存
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 创建多个环境和智能体
        environments = []
        agents = []
        
        for i in range(3):
            env = create_test_env(config=self.config)
            agent = create_test_agent(env=env, config=self.config)
            environments.append(env)
            agents.append(agent)
        
        # 记录峰值内存
        peak_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 清理资源
        for env in environments:
            env.close()
        del environments, agents
        gc.collect()
        
        # 记录清理后内存
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        memory_metrics = {
            'initial_memory': initial_memory,
            'peak_memory': peak_memory,
            'final_memory': final_memory,
            'memory_increase': peak_memory - initial_memory,
            'memory_leak': final_memory - initial_memory
        }
        
        # 验证内存使用
        assert memory_metrics['memory_increase'] < 500, f"内存增长过多: {memory_metrics['memory_increase']:.2f}MB"
        assert memory_metrics['memory_leak'] < 100, f"可能存在内存泄漏: {memory_metrics['memory_leak']:.2f}MB"
        
        self.logger.info(f"内存使用测试完成: {memory_metrics}")
        
        return memory_metrics
    
    def test_error_handling(self):
        """测试错误处理"""
        self.logger.info("开始测试错误处理...")
        
        # 测试无效配置
        invalid_config = create_test_config({
            'env.max_steps': -1,  # 无效值
            'agent.lr': -0.1      # 无效值
        })
        
        # 应该能够处理无效配置而不崩溃
        try:
            env = create_test_env(config=invalid_config)
            # 环境应该使用默认值或处理无效值
            assert env.max_steps > 0, "环境应该处理无效的max_steps"
        except Exception as e:
            self.logger.info(f"环境正确处理了无效配置: {e}")
        
        # 测试智能体错误处理
        try:
            agent = create_test_agent(config=invalid_config)
            # 智能体应该使用默认值或处理无效值
            assert agent.lr > 0, "智能体应该处理无效的学习率"
        except Exception as e:
            self.logger.info(f"智能体正确处理了无效配置: {e}")
        
        self.logger.info("错误处理测试完成")
    
    @pytest.mark.slow
    def test_complete_workflow(self):
        """测试完整工作流程"""
        self.logger.info("开始测试完整工作流程...")
        
        workflow_results = {}
        
        # 1. 创建和初始化
        self.logger.info("步骤1: 创建和初始化")
        self.trainer = create_test_trainer(config=self.config)
        workflow_results['initialization'] = 'success'
        
        # 2. 短期训练
        self.logger.info("步骤2: 短期训练")
        training_metrics, training_time = self.test_short_training_pipeline()
        workflow_results['training'] = training_metrics
        workflow_results['training']['time'] = training_time
        
        # 3. 模型保存
        self.logger.info("步骤3: 模型保存")
        model_path = os.path.join(self.temp_dir, 'workflow_model.pth')
        self.trainer.agent.save(model_path)
        workflow_results['model_save'] = os.path.exists(model_path)
        
        # 4. 评估
        self.logger.info("步骤4: 评估")
        eval_results = self.test_evaluation_pipeline()
        workflow_results['evaluation'] = eval_results
        
        # 5. 模型加载
        self.logger.info("步骤5: 模型加载")
        new_agent = create_test_agent(env=self.trainer.env, config=self.config)
        new_agent.load(model_path)
        workflow_results['model_load'] = 'success'
        
        # 验证完整工作流程
        assert workflow_results['initialization'] == 'success', "初始化失败"
        assert workflow_results['model_save'], "模型保存失败"
        assert workflow_results['model_load'] == 'success', "模型加载失败"
        
        self.logger.info(f"完整工作流程测试成功: {workflow_results}")
        
        return workflow_results
