<?xml version="1.0" encoding="utf-8"?><testsuites name="pytest tests"><testsuite name="pytest" errors="0" failures="1" skipped="9" tests="40" time="305.367" timestamp="2025-06-26T09:22:23.842989+08:00" hostname="DESKTOP-BKSHHBC"><testcase classname="torch_game.tests.integration.test_end_to_end.TestEndToEnd" name="test_mock_environment_pipeline" time="0.256" /><testcase classname="torch_game.tests.integration.test_end_to_end.TestEndToEnd" name="test_short_training_pipeline" time="26.604" /><testcase classname="torch_game.tests.integration.test_end_to_end.TestEndToEnd" name="test_model_save_load" time="0.052" /><testcase classname="torch_game.tests.integration.test_end_to_end.TestEndToEnd" name="test_evaluation_pipeline" time="4.157" /><testcase classname="torch_game.tests.integration.test_end_to_end.TestEndToEnd" name="test_memory_usage" time="0.059" /><testcase classname="torch_game.tests.integration.test_end_to_end.TestEndToEnd" name="test_error_handling" time="0.039" /><testcase classname="torch_game.tests.integration.test_real_game_integration.TestRealGameIntegration" name="test_game_window_detection" time="1.155"><skipped type="pytest.skip" message="真实游戏环境不可用">E:\xy2_yolo_auxiliary\torch_game\tests\conftest.py:218: 真实游戏环境不可用</skipped></testcase><testcase classname="torch_game.tests.integration.test_real_game_integration.TestRealGameIntegration" name="test_screenshot_capture" time="0.049"><skipped type="pytest.skip" message="真实游戏环境不可用">E:\xy2_yolo_auxiliary\torch_game\tests\conftest.py:218: 真实游戏环境不可用</skipped></testcase><testcase classname="torch_game.tests.integration.test_real_game_integration.TestRealGameIntegration" name="test_game_state_detection" time="0.043"><skipped type="pytest.skip" message="真实游戏环境不可用">E:\xy2_yolo_auxiliary\torch_game\tests\conftest.py:218: 真实游戏环境不可用</skipped></testcase><testcase classname="torch_game.tests.integration.test_real_game_integration.TestRealGameIntegration" name="test_block_analysis" time="0.071"><skipped type="pytest.skip" message="真实游戏环境不可用">E:\xy2_yolo_auxiliary\torch_game\tests\conftest.py:218: 真实游戏环境不可用</skipped></testcase><testcase classname="torch_game.tests.integration.test_real_game_integration.TestRealGameIntegration" name="test_game_ready_check" time="0.047"><skipped type="pytest.skip" message="真实游戏环境不可用">E:\xy2_yolo_auxiliary\torch_game\tests\conftest.py:218: 真实游戏环境不可用</skipped></testcase><testcase classname="torch_game.tests.integration.test_real_game_integration.TestRealGameIntegration" name="test_environment_creation" time="0.043"><skipped type="pytest.skip" message="真实游戏环境不可用">E:\xy2_yolo_auxiliary\torch_game\tests\conftest.py:218: 真实游戏环境不可用</skipped></testcase><testcase classname="torch_game.tests.integration.test_real_game_integration.TestRealGameIntegration" name="test_environment_reset" time="0.077"><skipped type="pytest.skip" message="真实游戏环境不可用">E:\xy2_yolo_auxiliary\torch_game\tests\conftest.py:218: 真实游戏环境不可用</skipped></testcase><testcase classname="torch_game.tests.integration.test_real_game_integration.TestRealGameIntegration" name="test_environment_step" time="0.051"><skipped type="pytest.skip" message="真实游戏环境不可用">E:\xy2_yolo_auxiliary\torch_game\tests\conftest.py:218: 真实游戏环境不可用</skipped></testcase><testcase classname="torch_game.tests.integration.test_real_game_integration.TestRealGameIntegration" name="test_agent_integration" time="0.053"><skipped type="pytest.skip" message="真实游戏环境不可用">E:\xy2_yolo_auxiliary\torch_game\tests\conftest.py:218: 真实游戏环境不可用</skipped></testcase><testcase classname="torch_game.tests.integration.test_training_pipeline.TestTrainingPipeline" name="test_buffer_operations" time="0.103" /><testcase classname="torch_game.tests.integration.test_training_pipeline.TestTrainingPipeline" name="test_data_collection" time="2.151" /><testcase classname="torch_game.tests.integration.test_training_pipeline.TestTrainingPipeline" name="test_agent_update" time="0.095" /><testcase classname="torch_game.tests.integration.test_training_pipeline.TestTrainingPipeline" name="test_training_loop" time="42.927" /><testcase classname="torch_game.tests.integration.test_training_pipeline.TestTrainingPipeline" name="test_learning_progress" time="164.564" /><testcase classname="torch_game.tests.integration.test_training_pipeline.TestTrainingPipeline" name="test_checkpoint_system" time="58.245" /><testcase classname="torch_game.tests.test_optimized_env.TestOptimizedMatch3Env" name="test_init" time="0.089" /><testcase classname="torch_game.tests.test_optimized_env.TestOptimizedMatch3Env" name="test_reset" time="0.088" /><testcase classname="torch_game.tests.test_optimized_env.TestOptimizedMatch3Env" name="test_step_invalid_action" time="0.299" /><testcase classname="torch_game.tests.test_optimized_env.TestOptimizedMatch3Env" name="test_step_valid_action" time="0.285" /><testcase classname="torch_game.tests.test_optimized_env.TestOptimizedMatch3Env" name="test_observation_space" time="0.087" /><testcase classname="torch_game.tests.test_optimized_env.TestOptimizedMatch3Env" name="test_episode_done_conditions" time="0.089" /><testcase classname="torch_game.tests.test_optimized_env.TestOptimizedMatch3Env" name="test_close" time="0.086" /><testcase classname="torch_game.tests.test_optimized_env.TestOptimizedStateConverter" name="test_convert_to_observation" time="0.081" /><testcase classname="torch_game.tests.test_optimized_env.TestSmartActionMapper" name="test_map_action_to_block" time="0.078" /><testcase classname="torch_game.tests.test_optimized_env.TestOptimizedRewardCalculator" name="test_calculate_reward_invalid_action" time="0.081" /><testcase classname="torch_game.tests.test_optimized_env.TestOptimizedRewardCalculator" name="test_calculate_reward_elimination" time="0.123" /><testcase classname="torch_game.tests.test_optimized_env.TestOptimizedRewardCalculator" name="test_calculate_reward_storage_penalty" time="0.114" /><testcase classname="torch_game.tests.test_ppo_agent.TestPPOAgent" name="test_compute_advantages" time="0.176" /><testcase classname="torch_game.tests.test_ppo_agent.TestPPOAgent" name="test_compute_returns" time="0.083" /><testcase classname="torch_game.tests.test_ppo_agent.TestPPOAgent" name="test_init" time="0.084" /><testcase classname="torch_game.tests.test_ppo_agent.TestPPOAgent" name="test_save_load" time="1.049" /><testcase classname="torch_game.tests.test_ppo_agent.TestPPOAgent" name="test_select_action" time="0.775" /><testcase classname="torch_game.tests.test_ppo_agent.TestPPOAgent" name="test_update" time="0.123" /><testcase classname="torch_game.tests.test_replay_buffer.TestReplayBuffer" name="test_add" time="0.122"><failure message="AttributeError: 'ReplayBuffer' object has no attribute 'add'">tests\test_replay_buffer.py:31: in test_add
    self.buffer.add(
E   AttributeError: 'ReplayBuffer' object has no attribute 'add'</failure></testcase></testsuite></testsuites>