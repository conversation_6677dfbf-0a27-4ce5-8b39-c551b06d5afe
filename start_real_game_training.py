#!/usr/bin/env python3
"""
真实游戏强化学习训练启动脚本
确保在运行前：
1. 游戏窗口 "最强祖师" 已打开
2. 游戏处于可玩状态
3. 模型文件 models/sanxiao/best.pt 存在
"""

import os
import sys
import time

def check_prerequisites():
    """检查训练前置条件"""
    print("🔍 检查训练前置条件...")
    
    # 检查模型文件
    model_path = "../models/sanxiao/best.pt"
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return False
    print(f"✅ 模型文件存在: {model_path}")
    
    # 检查必要的模块
    try:
        from s4_三消 import TripleEliminationGameAssistant
        from assist import AssistBasicToolkit
        print("✅ 游戏助手模块可用")
    except ImportError as e:
        print(f"❌ 游戏助手模块导入失败: {e}")
        return False
    
    # 检查torch_game模块
    try:
        from torch_game.core.env import OptimizedMatch3Env
        from torch_game.core.agents import PPOAgent
        from torch_game.core.utils.trainer import Trainer
        print("✅ torch_game模块可用")
    except ImportError as e:
        print(f"❌ torch_game模块导入失败: {e}")
        return False
    
    return True

def check_game_window():
    """检查游戏窗口"""
    print("\n🎮 检查游戏窗口...")
    try:
        from assist import AssistBasicToolkit
        assist = AssistBasicToolkit()
        hwnd = assist.register_and_bind_window_objects('最强祖师')
        if hwnd == 0:
            print("❌ 未找到游戏窗口 '最强祖师'")
            print("   请确保游戏已打开并且窗口标题为 '最强祖师'")
            return False
        print(f"✅ 找到游戏窗口，句柄: {hwnd}")
        return True
    except Exception as e:
        print(f"❌ 检查游戏窗口失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 真实游戏强化学习训练启动器")
    print("=" * 60)
    
    # 检查前置条件
    if not check_prerequisites():
        print("\n❌ 前置条件检查失败，请解决上述问题后重试")
        return
    
    # 检查游戏窗口
    if not check_game_window():
        print("\n❌ 游戏窗口检查失败，请确保游戏已打开")
        return
    
    print("\n✅ 所有前置条件检查通过！")
    print("\n🚀 准备启动训练...")
    
    # 倒计时
    for i in range(3, 0, -1):
        print(f"   {i}秒后开始训练...")
        time.sleep(1)
    
    print("\n🎮 启动训练脚本...")
    print("=" * 60)
    
    # 运行训练脚本
    try:
        os.system("python examples/train_optimized_game.py")
    except Exception as e:
        print(f"❌ 启动训练失败: {e}")
        return
    
    print("\n🎉 训练脚本执行完成！")

if __name__ == "__main__":
    main()
