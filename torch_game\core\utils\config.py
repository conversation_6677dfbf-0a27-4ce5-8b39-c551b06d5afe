"""
配置管理
用于管理训练和环境的各种参数
"""

import json
import os
import yaml
from typing import Dict, Any, Optional, Union

class Config:
    """配置管理类"""
    
    def __init__(self, config_dict: Optional[Dict[str, Any]] = None, config_path: Optional[str] = None):
        """
        初始化配置
        
        Args:
            config_dict: 配置字典
            config_path: 配置文件路径
        """
        self.config = {}
        
        if config_dict is not None:
            self.config.update(config_dict)
            
        if config_path is not None:
            self.load(config_path)
            
    def __getitem__(self, key: str) -> Any:
        """
        获取配置项
        
        Args:
            key: 配置项键名
            
        Returns:
            value: 配置项值
        """
        return self.config[key]
        
    def __setitem__(self, key: str, value: Any):
        """
        设置配置项
        
        Args:
            key: 配置项键名
            value: 配置项值
        """
        self.config[key] = value
        
    def __contains__(self, key: str) -> bool:
        """
        检查配置项是否存在
        
        Args:
            key: 配置项键名
            
        Returns:
            exists: 配置项是否存在
        """
        return key in self.config
        
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置项，支持点号分隔的嵌套键名，如果不存在则返回默认值

        Args:
            key: 配置项键名，支持点号分隔的嵌套键名（如'env.max_steps'）
            default: 默认值

        Returns:
            value: 配置项值或默认值
        """
        if '.' in key:
            # 处理嵌套键名
            keys = key.split('.')
            value = self.config
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return default
            return value
        else:
            # 处理简单键名
            return self.config.get(key, default)
        
    def update(self, config_dict: Dict[str, Any]):
        """
        更新配置
        
        Args:
            config_dict: 配置字典
        """
        self.config.update(config_dict)
        
    def load(self, config_path: str):
        """
        从文件加载配置，支持JSON和YAML格式

        Args:
            config_path: 配置文件路径
        """
        with open(config_path, 'r', encoding='utf-8') as f:
            if config_path.endswith('.yaml') or config_path.endswith('.yml'):
                self.config.update(yaml.safe_load(f))
            else:
                self.config.update(json.load(f))
            
    def save(self, config_path: str):
        """
        保存配置到文件
        
        Args:
            config_path: 配置文件路径
        """
        os.makedirs(os.path.dirname(config_path), exist_ok=True)
        with open(config_path, 'w') as f:
            json.dump(self.config, f, indent=2)
            
    def __str__(self) -> str:
        """
        返回配置的字符串表示
        
        Returns:
            config_str: 配置的字符串表示
        """
        return json.dumps(self.config, indent=2)
        
    def __repr__(self) -> str:
        """
        返回配置的字符串表示
        
        Returns:
            config_str: 配置的字符串表示
        """
        return self.__str__()
        
    @classmethod
    def from_file(cls, config_path: str) -> 'Config':
        """
        从文件创建配置
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            config: 配置对象
        """
        return cls(config_path=config_path)
        
    @classmethod
    def default_match3_config(cls) -> 'Config':
        """
        创建Match-3游戏的默认配置
        
        Returns:
            config: 配置对象
        """
        return cls({
            # 环境配置
            'env': {
                'board_size': [8, 8],
                'n_colors': 6,
                'min_match': 3,
                'max_steps': 100
            },
            
            # 智能体配置
            'agent': {
                'type': 'ppo',
                'hidden_dim': 256,
                'lr': 3e-4,
                'gamma': 0.99,
                'epsilon': 0.2,
                'value_coef': 0.5,
                'entropy_coef': 0.01
            },
            
            # 训练配置
            'training': {
                'n_episodes': 10000,
                'batch_size': 64,
                'update_interval': 2048,
                'n_epochs': 10,
                'eval_interval': 100,
                'save_interval': 500,
                'log_interval': 10
            },
            
            # 日志配置
            'logging': {
                'log_dir': 'logs',
                'model_dir': 'models'
            }
        })