"""
内存性能分析测试
详细分析内存使用模式和潜在的内存泄漏
"""

import pytest
import os
import sys
import time
import gc
import logging
from typing import Dict, Any, List
import numpy as np
import torch
import psutil

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from torch_game.core.env import OptimizedMatch3Env
from torch_game.core.agents import PPOAgent
from torch_game.core.utils.trainer import Trainer
from torch_game.tests.utils import (
    TestConfig, create_test_config, create_test_env, create_test_agent,
    MockGameAssistant, TestDataGenerator,
    setup_test_logging, measure_execution_time
)


class TestMemoryProfiling:
    """内存性能分析测试类"""
    
    @classmethod
    def setup_class(cls):
        """类级别的设置"""
        setup_test_logging()
        cls.logger = logging.getLogger(__name__)
        cls.process = psutil.Process()
        
        cls.config = create_test_config({
            'env.max_steps': 30,
            'agent.hidden_dim': 128,
            'training.batch_size': 16,
            'training.update_interval': 20
        })
        
        cls.data_generator = TestDataGenerator(seed=42)
    
    def setup_method(self):
        """每个测试方法前的设置"""
        # 强制垃圾回收
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        # 记录初始内存
        self.initial_memory = self.get_memory_usage()
        self.logger.info(f"测试开始时内存使用: {self.initial_memory}")
        
        self.env = None
        self.agent = None
        self.trainer = None
    
    def teardown_method(self):
        """每个测试方法后的清理"""
        if self.env:
            self.env.close()
        if self.trainer and hasattr(self.trainer, 'writer'):
            self.trainer.writer.close()
        
        # 清理变量
        self.env = None
        self.agent = None
        self.trainer = None
        
        # 强制垃圾回收
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        # 记录最终内存
        final_memory = self.get_memory_usage()
        memory_diff = final_memory['rss_mb'] - self.initial_memory['rss_mb']
        
        self.logger.info(f"测试结束时内存使用: {final_memory}")
        self.logger.info(f"内存变化: {memory_diff:.2f}MB")
        
        # 检查内存泄漏
        if memory_diff > 50:  # 如果内存增长超过50MB
            self.logger.warning(f"可能存在内存泄漏，内存增长: {memory_diff:.2f}MB")
    
    def get_memory_usage(self) -> Dict[str, float]:
        """获取当前内存使用情况"""
        memory_info = self.process.memory_info()
        return {
            'rss_mb': memory_info.rss / 1024 / 1024,  # 物理内存
            'vms_mb': memory_info.vms / 1024 / 1024,  # 虚拟内存
            'percent': self.process.memory_percent()   # 内存使用百分比
        }
    
    def test_environment_memory_lifecycle(self):
        """测试环境内存生命周期"""
        self.logger.info("开始测试环境内存生命周期...")
        
        memory_timeline = []
        
        # 记录初始状态
        memory_timeline.append(('initial', self.get_memory_usage()))
        
        # 创建环境
        mock_assistant = MockGameAssistant()
        self.env = OptimizedMatch3Env(
            game_assistant=mock_assistant,
            max_steps=self.config.get('env.max_steps'),
            n_colors=self.config.get('env.n_colors'),
            storage_capacity=self.config.get('env.storage_capacity')
        )
        memory_timeline.append(('after_env_creation', self.get_memory_usage()))
        
        # 重置环境多次
        for i in range(10):
            observation = self.env.reset()
            memory_timeline.append((f'reset_{i}', self.get_memory_usage()))
        
        # 执行多个步骤
        for step in range(20):
            action = self.env.action_space.sample()
            observation, reward, done, info = self.env.step(action)
            if done:
                observation = self.env.reset()
            
            if step % 5 == 0:
                memory_timeline.append((f'step_{step}', self.get_memory_usage()))
        
        # 关闭环境
        self.env.close()
        memory_timeline.append(('after_env_close', self.get_memory_usage()))
        
        # 删除环境引用
        self.env = None
        gc.collect()
        memory_timeline.append(('after_gc', self.get_memory_usage()))
        
        # 分析内存变化
        self.logger.info("环境内存生命周期分析:")
        for stage, memory in memory_timeline:
            self.logger.info(f"  {stage}: {memory['rss_mb']:.2f}MB")
        
        # 验证内存没有显著泄漏
        initial_memory = memory_timeline[0][1]['rss_mb']
        final_memory = memory_timeline[-1][1]['rss_mb']
        memory_leak = final_memory - initial_memory
        
        assert memory_leak < 20, f"环境可能存在内存泄漏: {memory_leak:.2f}MB"
        
        return memory_timeline
    
    def test_agent_memory_usage(self):
        """测试智能体内存使用"""
        self.logger.info("开始测试智能体内存使用...")
        
        memory_timeline = []
        
        # 创建环境
        self.env = create_test_env(config=self.config)
        memory_timeline.append(('after_env', self.get_memory_usage()))
        
        # 创建智能体
        self.agent = create_test_agent(env=self.env, config=self.config)
        memory_timeline.append(('after_agent', self.get_memory_usage()))
        
        # 执行多次推理
        state_dim = np.prod(self.env.observation_space.shape)
        test_states = [
            np.random.random(state_dim).astype(np.float32) 
            for _ in range(100)
        ]
        
        for i, state in enumerate(test_states):
            action, log_prob = self.agent.select_action(state)
            
            if i % 20 == 0:
                memory_timeline.append((f'inference_{i}', self.get_memory_usage()))
        
        # 执行多次训练更新
        action_dim = self.env.action_space.n
        batch_size = self.config.get('training.batch_size')
        
        for i in range(10):
            batch_data = self.data_generator.generate_batch_data(
                batch_size, state_dim, action_dim
            )
            update_info = self.agent.update(batch_data)
            
            if i % 3 == 0:
                memory_timeline.append((f'update_{i}', self.get_memory_usage()))
        
        # 分析内存使用
        self.logger.info("智能体内存使用分析:")
        for stage, memory in memory_timeline:
            self.logger.info(f"  {stage}: {memory['rss_mb']:.2f}MB")
        
        # 计算智能体内存开销
        env_memory = memory_timeline[0][1]['rss_mb']
        agent_memory = memory_timeline[1][1]['rss_mb']
        agent_overhead = agent_memory - env_memory
        
        assert agent_overhead < 300, f"智能体内存开销过大: {agent_overhead:.2f}MB"
        
        return memory_timeline
    
    def test_training_memory_growth(self):
        """测试训练过程中的内存增长"""
        self.logger.info("开始测试训练内存增长...")
        
        # 创建训练器
        self.env = create_test_env(config=self.config)
        self.agent = create_test_agent(env=self.env, config=self.config)
        
        # 创建训练配置
        training_config = create_test_config({
            'training.total_episodes': 10,
            'training.update_interval': self.config.get('training.update_interval'),
            'training.batch_size': self.config.get('training.batch_size'),
            'training.n_epochs': 2,
            'training.log_interval': 2,
            'training.eval_interval': 5,
            'training.save_interval': 20,
            'logging.model_dir': '/tmp'
        })

        self.trainer = Trainer(self.agent, self.env, training_config)
        
        memory_during_training = []
        
        # 记录训练前内存
        memory_during_training.append(('before_training', self.get_memory_usage()))
        
        # 模拟训练过程，分阶段记录内存
        original_train = self.trainer.train
        
        def monitored_train():
            episode = 0
            while episode < self.trainer.n_episodes:
                # 收集数据
                self.trainer.collect_rollout()
                memory_during_training.append((f'after_rollout_{episode}', self.get_memory_usage()))
                
                # 更新智能体
                self.trainer.update_agent()
                memory_during_training.append((f'after_update_{episode}', self.get_memory_usage()))
                
                episode += 1
                
                # 定期垃圾回收
                if episode % 3 == 0:
                    gc.collect()
                    memory_during_training.append((f'after_gc_{episode}', self.get_memory_usage()))
        
        # 执行监控的训练
        monitored_train()
        
        # 分析内存增长趋势
        self.logger.info("训练过程内存变化:")
        for stage, memory in memory_during_training:
            self.logger.info(f"  {stage}: {memory['rss_mb']:.2f}MB")
        
        # 检查内存增长趋势
        initial_memory = memory_during_training[0][1]['rss_mb']
        final_memory = memory_during_training[-1][1]['rss_mb']
        total_growth = final_memory - initial_memory
        
        # 计算平均每回合内存增长
        num_episodes = self.trainer.n_episodes
        growth_per_episode = total_growth / num_episodes
        
        assert total_growth < 100, f"训练过程内存增长过多: {total_growth:.2f}MB"
        assert growth_per_episode < 10, f"每回合内存增长过多: {growth_per_episode:.2f}MB"
        
        return {
            'total_growth_mb': total_growth,
            'growth_per_episode_mb': growth_per_episode,
            'memory_timeline': memory_during_training
        }
    
    def test_batch_size_memory_scaling(self):
        """测试批次大小对内存的影响"""
        self.logger.info("开始测试批次大小内存扩展...")
        
        batch_memory_usage = []
        batch_sizes = [4, 8, 16, 32, 64]
        
        for batch_size in batch_sizes:
            self.logger.info(f"测试批次大小: {batch_size}")
            
            # 清理内存
            gc.collect()
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            # 记录基线内存
            baseline_memory = self.get_memory_usage()
            
            # 创建配置
            config = create_test_config({
                'training.batch_size': batch_size,
                'agent.hidden_dim': 128
            })
            
            # 创建环境和智能体
            env = create_test_env(config=config)
            agent = create_test_agent(env=env, config=config)
            
            # 记录创建后内存
            after_creation_memory = self.get_memory_usage()
            
            # 生成训练数据
            state_dim = np.prod(env.observation_space.shape)
            action_dim = env.action_space.n
            batch_data = self.data_generator.generate_batch_data(
                batch_size, state_dim, action_dim
            )
            
            # 记录数据生成后内存
            after_data_memory = self.get_memory_usage()
            
            # 执行训练更新
            update_info = agent.update(batch_data)
            
            # 记录更新后内存
            after_update_memory = self.get_memory_usage()
            
            # 计算内存开销
            creation_overhead = after_creation_memory['rss_mb'] - baseline_memory['rss_mb']
            data_overhead = after_data_memory['rss_mb'] - after_creation_memory['rss_mb']
            update_overhead = after_update_memory['rss_mb'] - after_data_memory['rss_mb']
            
            batch_memory_usage.append({
                'batch_size': batch_size,
                'creation_overhead_mb': creation_overhead,
                'data_overhead_mb': data_overhead,
                'update_overhead_mb': update_overhead,
                'total_overhead_mb': after_update_memory['rss_mb'] - baseline_memory['rss_mb']
            })
            
            # 清理
            env.close()
            del env, agent, batch_data
            gc.collect()
        
        # 分析内存扩展性
        self.logger.info("批次大小内存扩展分析:")
        for usage in batch_memory_usage:
            self.logger.info(f"  批次 {usage['batch_size']}: "
                           f"总开销 {usage['total_overhead_mb']:.2f}MB, "
                           f"数据开销 {usage['data_overhead_mb']:.2f}MB, "
                           f"更新开销 {usage['update_overhead_mb']:.2f}MB")
        
        # 验证内存扩展的合理性
        for i in range(1, len(batch_memory_usage)):
            current = batch_memory_usage[i]
            previous = batch_memory_usage[i-1]
            
            # 批次大小翻倍，内存使用不应该超过3倍
            size_ratio = current['batch_size'] / previous['batch_size']
            memory_ratio = current['total_overhead_mb'] / max(previous['total_overhead_mb'], 1)
            
            assert memory_ratio < size_ratio * 3, \
                f"内存增长不合理，批次大小比例: {size_ratio}, 内存比例: {memory_ratio}"
        
        return batch_memory_usage
    
    @pytest.mark.slow
    def test_long_running_memory_stability(self):
        """测试长时间运行的内存稳定性"""
        self.logger.info("开始测试长时间运行内存稳定性...")
        
        # 创建环境和智能体
        self.env = create_test_env(config=self.config)
        self.agent = create_test_agent(env=self.env, config=self.config)
        
        memory_samples = []
        state_dim = np.prod(self.env.observation_space.shape)
        action_dim = self.env.action_space.n
        
        # 长时间运行模拟
        for cycle in range(20):  # 20个周期
            cycle_start_memory = self.get_memory_usage()
            
            # 每个周期执行多次操作
            for _ in range(10):
                # 推理
                test_state = np.random.random(state_dim).astype(np.float32)
                action, _ = self.agent.select_action(test_state)
                
                # 训练
                batch_data = self.data_generator.generate_batch_data(
                    8, state_dim, action_dim
                )
                self.agent.update(batch_data)
            
            cycle_end_memory = self.get_memory_usage()
            
            memory_samples.append({
                'cycle': cycle,
                'start_memory_mb': cycle_start_memory['rss_mb'],
                'end_memory_mb': cycle_end_memory['rss_mb'],
                'cycle_growth_mb': cycle_end_memory['rss_mb'] - cycle_start_memory['rss_mb']
            })
            
            # 定期垃圾回收
            if cycle % 5 == 0:
                gc.collect()
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
        
        # 分析内存稳定性
        total_growth = memory_samples[-1]['end_memory_mb'] - memory_samples[0]['start_memory_mb']
        avg_cycle_growth = np.mean([sample['cycle_growth_mb'] for sample in memory_samples])
        max_cycle_growth = np.max([sample['cycle_growth_mb'] for sample in memory_samples])
        
        stability_metrics = {
            'total_growth_mb': total_growth,
            'avg_cycle_growth_mb': avg_cycle_growth,
            'max_cycle_growth_mb': max_cycle_growth,
            'num_cycles': len(memory_samples)
        }
        
        # 验证内存稳定性
        assert total_growth < 50, f"长时间运行内存增长过多: {total_growth:.2f}MB"
        assert avg_cycle_growth < 2, f"平均周期内存增长过多: {avg_cycle_growth:.2f}MB"
        
        self.logger.info(f"长时间运行内存稳定性测试完成: {stability_metrics}")
        
        return stability_metrics
