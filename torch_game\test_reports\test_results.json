{"unit": {"test_type": "unit_tests", "return_code": 4, "duration": 3.646099328994751, "stdout": "ERROR: file or directory not found: E:\\xy2_yolo_auxiliary\\torch_game\\tests\\test_*.py\n\n============================= test session starts =============================\nplatform win32 -- Python 3.10.10, pytest-8.4.1, pluggy-1.6.0\nbenchmark: 5.1.0 (defaults: timer=time.perf_counter disable_gc=False min_rounds=5 min_time=0.000005 max_time=1.0 calibration_precision=10 warmup=False warmup_iterations=100000)\nrootdir: E:\\xy2_yolo_auxiliary\nconfigfile: pytest.ini\nplugins: anyio-4.8.0, benchmark-5.1.0, cov-6.2.1, html-4.1.1, metadata-3.1.1\ncollected 0 items\n\n- generated xml file: E:\\xy2_yolo_auxiliary\\torch_game\\test_reports\\unit_tests.xml -\n============================ no tests ran in 0.01s ============================", "stderr": "", "summary": "No summary available", "success": false}, "integration": {"test_type": "integration_tests", "return_code": 1, "duration": 456.0714612007141, "stdout": "============================= test session starts =============================\nplatform win32 -- Python 3.10.10, pytest-8.4.1, pluggy-1.6.0\nbenchmark: 5.1.0 (defaults: timer=time.perf_counter disable_gc=False min_rounds=5 min_time=0.000005 max_time=1.0 calibration_precision=10 warmup=False warmup_iterations=100000)\nrootdir: E:\\xy2_yolo_auxiliary\nconfigfile: pytest.ini\nplugins: anyio-4.8.0, benchmark-5.1.0, cov-6.2.1, html-4.1.1, metadata-3.1.1\ncollected 24 items / 10 deselected / 14 selected\n\ntests\\integration\\test_end_to_end.py .......                             [ 50%]\ntests\\integration\\test_training_pipeline.py ......F                      [100%]\n\n================================== FAILURES ===================================\n_________________ TestTrainingPipeline.test_extended_training _________________\ntests\\integration\\test_training_pipeline.py:490: in test_extended_training\n    assert is_valid, f\"扩展训练结果验证失败: {errors}\"\nE   AssertionError: 扩展训练结果验证失败: ['缺少必需的键: avg_length']\nE   assert False\n---------------------------- Captured stdout call -----------------------------\n\\U0001f3af \\u5f00\\u59cb\\u8bad\\u7ec3...\\n\\U0001f4ca \\u8ba1\\u5212\\u8bad\\u7ec3 10 \\u4e2a\\u56de\\u5408\\n\\U0001f504 \\u6bcf 20 \\u6b65\\u66f4\\u65b0\\u4e00\\u6b21\\n\\U0001f4e6 \\u6279\\u6b21\\u5927\\u5c0f: 8\\n\\U0001f501 \\u6bcf\\u6b21\\u66f4\\u65b0\\u8bad\\u7ec3 3 \\u8f6e\\n--------------------------------------------------\\n\\U0001f3ae \\u5f00\\u59cb\\u7b2c 1/10 \\u4e2a\\u8bad\\u7ec3\\u56de\\u5408...\\n  \\U0001f4e5 \\u6536\\u96c6\\u8f68\\u8ff9\\u6570\\u636e\\uff0820 \\u6b65\\uff09...\\n\\u6b63\\u5728\\u786e\\u4fdd\\u6e38\\u620f\\u51c6\\u5907\\u5c31\\u7eea...\\n\\u6e38\\u620f\\u5df2\\u51c6\\u5907\\u5c31\\u7eea\\n    \\U0001f504 \\u5f00\\u59cb\\u6536\\u96c6 20 \\u6b65\\u6570\\u636e...\\n    \\U0001f4ca \\u8fdb\\u5ea6: 0/20 \\u6b65\\uff0c\\u5f53\\u524d\\u5956\\u52b1: 0.000\\n    \\U0001f4ca \\u8fdb\\u5ea6: 5/20 \\u6b65\\uff0c\\u5f53\\u524d\\u5956\\u52b1: 0.000\\n    \\U0001f4ca \\u8fdb\\u5ea6: 10/20 \\u6b65\\uff0c\\u5f53\\u524d\\u5956\\u52b1: 0.000\\n    \\U0001f4ca \\u8fdb\\u5ea6: 15/20 \\u6b65\\uff0c\\u5f53\\u524d\\u5956\\u52b1: 0.000\\nEpisode\\u7ed3\\u675f\\uff1a\\u8fbe\\u5230\\u6700\\u5927\\u6b65\\u6570 20\\n    \\U0001f3af Episode 1 \\u5b8c\\u6210\\uff0c\\u5956\\u52b1: 0.000\\n\\u6b63\\u5728\\u786e\\u4fdd\\u6e38\\u620f\\u51c6\\u5907\\u5c31\\u7eea...\\n\\u6e38\\u620f\\u5df2\\u51c6\\u5907\\u5c31\\u7eea\\n    \\u2705 \\u6570\\u636e\\u6536\\u96c6\\u5b8c\\u6210\\uff0c\\u5171\\u5b8c\\u6210 1 \\u4e2aepisode\\n  \\u2705 \\u8f68\\u8ff9\\u6536\\u96c6\\u5b8c\\u6210\\uff0c\\u8017\\u65f6: 4.11\\u79d2\\n  \\U0001f9e0 \\u66f4\\u65b0\\u667a\\u80fd\\u4f53\\uff083 \\u8f6e\\u8bad\\u7ec3\\uff09...\\n  \\u2705 \\u667a\\u80fd\\u4f53\\u66f4\\u65b0\\u5b8c\\u6210\\uff0c\\u8017\\u65f6: 0.01\\u79d2\\n  \\U0001f4ca \\u8bb0\\u5f55\\u8bad\\u7ec3\\u4fe1\\u606f...\\n\\nEpisode 0 (Total Steps: 0)\\nAverage Reward: 0.00\\nTotal Loss: -0.0398\\nPolicy Loss: 0.0292\\nValue Loss: 0.0000\\nEntropy: 6.9032\\n  \\U0001f3af \\u6267\\u884c\\u8bc4\\u4f30...\\n\\u6b63\\u5728\\u786e\\u4fdd\\u6e38\\u620f\\u51c6\\u5907\\u5c31\\u7eea...\\n\\u6e38\\u620f\\u5df2\\u51c6\\u5907\\u5c31\\u7eea\\nEpisode\\u7ed3\\u675f\\uff1a\\u8fbe\\u5230\\u6700\\u5927\\u6b65\\u6570 20\\n\\u6b63\\u5728\\u786e\\u4fdd\\u6e38\\u620f\\u51c6\\u5907\\u5c31\\u7eea...\\n\\u6e38\\u620f\\u5df2\\u51c6\\u5907\\u5c31\\u7eea\\nEpisode\\u7ed3\\u675f\\uff1a\\u8fbe\\u5230\\u6700\\u5927\\u6b65\\u6570 20\\n\\u6b63\\u5728\\u786e\\u4fdd\\u6e38\\u620f\\u51c6\\u5907\\u5c31\\u7eea...\\n\\u6e38\\u620f\\u5df2\\u51c6\\u5907\\u5c31\\u7eea\\nEpisode\\u7ed3\\u675f\\uff1a\\u8fbe\\u5230\\u6700\\u5927\\u6b65\\u6570 20\\n\\u6b63\\u5728\\u786e\\u4fdd\\u6e38\\u620f\\u51c6\\u5907\\u5c31\\u7eea...\\n\\u6e38\\u620f\\u5df2\\u51c6\\u5907\\u5c31\\u7eea\\nEpisode\\u7ed3\\u675f\\uff1a\\u8fbe\\u5230\\u6700\\u5927\\u6b65\\u6570 20\\n\\u6b63\\u5728\\u786e\\u4fdd\\u6e38\\u620f\\u51c6\\u5907\\u5c31\\u7eea...\\n\\u6e38\\u620f\\u5df2\\u51c6\\u5907\\u5c31\\u7eea\\nEpisode\\u7ed3\\u675f\\uff1a\\u8fbe\\u5230\\u6700\\u5927\\u6b65\\u6570 20\\n  \\u2705 \\u8bc4\\u4f30\\u5b8c\\u6210\\uff0c\\u5e73\\u5747\\u5956\\u52b1: 0.0000\\uff0c\\u8017\\u65f6: 20.34\\u79d2\\n  \\U0001f3c6 \\u65b0\\u7684\\u6700\\u4f73\\u6a21\\u578b\\uff01\\u5956\\u52b1: 0.0000\\n  \\U0001f4be \\u4fdd\\u5b58\\u6a21\\u578b\\u68c0\\u67e5\\u70b9...\\n  \\U0001f3c1 \\u7b2c 1/10 \\u56de\\u5408\\u5b8c\\u6210\\uff0c\\u8017\\u65f6: 24.46\\u79d2\\n  \\U0001f4c8 \\u5df2\\u5b8c\\u6210episode\\u6570: 1\\n  \\U0001f3af \\u6700\\u8fd1\\u5956\\u52b1: 0.0000\\n------------------------------\\n\\U0001f3ae \\u5f00\\u59cb\\u7b2c 2/10 \\u4e2a\\u8bad\\u7ec3\\u56de\\u5408...\\n  \\U0001f4e5 \\u6536\\u96c6\\u8f68\\u8ff9\\u6570\\u636e\\uff0820 \\u6b65\\uff09...\\n\\u6b63\\u5728\\u786e\\u4fdd\\u6e38\\u620f\\u51c6\\u5907\\u5c31\\u7eea...\\n\\u6e38\\u620f\\u5df2\\u51c6\\u5907\\u5c31\\u7eea\\n    \\U0001f504 \\u5f00\\u59cb\\u6536\\u96c6 20 \\u6b65\\u6570\\u636e...\\n    \\U0001f4ca \\u8fdb\\u5ea6: 0/20 \\u6b65\\uff0c\\u5f53\\u524d\\u5956\\u52b1: 0.000\\n    \\U0001f4ca \\u8fdb\\u5ea6: 5/20 \\u6b65\\uff0c\\u5f53\\u524d\\u5956\\u52b1: 0.000\\n    \\U0001f4ca \\u8fdb\\u5ea6: 10/20 \\u6b65\\uff0c\\u5f53\\u524d\\u5956\\u52b1: 0.000\\n    \\U0001f4ca \\u8fdb\\u5ea6: 15/20 \\u6b65\\uff0c\\u5f53\\u524d\\u5956\\u52b1: 0.000\\nEpisode\\u7ed3\\u675f\\uff1a\\u8fbe\\u5230\\u6700\\u5927\\u6b65\\u6570 20\\n    \\U0001f3af Episode 1 \\u5b8c\\u6210\\uff0c\\u5956\\u52b1: 0.000\\n\\u6b63\\u5728\\u786e\\u4fdd\\u6e38\\u620f\\u51c6\\u5907\\u5c31\\u7eea...\\n\\u6e38\\u620f\\u5df2\\u51c6\\u5907\\u5c31\\u7eea\\n    \\u2705 \\u6570\\u636e\\u6536\\u96c6\\u5b8c\\u6210\\uff0c\\u5171\\u5b8c\\u6210 1 \\u4e2aepisode\\n  \\u2705 \\u8f68\\u8ff9\\u6536\\u96c6\\u5b8c\\u6210\\uff0c\\u8017\\u65f6: 4.09\\u79d2\\n  \\U0001f9e0 \\u66f4\\u65b0\\u667a\\u80fd\\u4f53\\uff083 \\u8f6e\\u8bad\\u7ec3\\uff09...\\n  \\u2705 \\u667a\\u80fd\\u4f53\\u66f4\\u65b0\\u5b8c\\u6210\\uff0c\\u8017\\u65f6: 0.01\\u79d2\\n  \\U0001f3c1 \\u7b2c 2/10 \\u56de\\u5408\\u5b8c\\u6210\\uff0c\\u8017\\u65f6: 4.11\\u79d2\\n  \\U0001f4c8 \\u5df2\\u5b8c\\u6210episode\\u6570: 2\\n  \\U0001f3af \\u6700\\u8fd1\\u5956\\u52b1: 0.0000\\n------------------------------\\n\\U0001f3ae \\u5f00\\u59cb\\u7b2c 3/10 \\u4e2a\\u8bad\\u7ec3\\u56de\\u5408...\\n  \\U0001f4e5 \\u6536\\u96c6\\u8f68\\u8ff9\\u6570\\u636e\\uff0820 \\u6b65\\uff09...\\n\\u6b63\\u5728\\u786e\\u4fdd\\u6e38\\u620f\\u51c6\\u5907\\u5c31\\u7eea...\\n\\u6e38\\u620f\\u5df2\\u51c6\\u5907\\u5c31\\u7eea\\n    \\U0001f504 \\u5f00\\u59cb\\u6536\\u96c6 20 \\u6b65\\u6570\\u636e...\\n    \\U0001f4ca \\u8fdb\\u5ea6: 0/20 \\u6b65\\uff0c\\u5f53\\u524d\\u5956\\u52b1: 0.000\\n    \\U0001f4ca \\u8fdb\\u5ea6: 5/20 \\u6b65\\uff0c\\u5f53\\u524d\\u5956\\u52b1: 0.000\\n    \\U0001f4ca \\u8fdb\\u5ea6: 10/20 \\u6b65\\uff0c\\u5f53\\u524d\\u5956\\u52b1: 0.000\\n    \\U0001f4ca \\u8fdb\\u5ea6: 15/20 \\u6b65\\uff0c\\u5f53\\u524d\\u5956\\u52b1: 0.000\\nEpisode\\u7ed3\\u675f\\uff1a\\u8fbe\\u5230\\u6700\\u5927\\u6b65\\u6570 20\\n    \\U0001f3af Episode 1 \\u5b8c\\u6210\\uff0c\\u5956\\u52b1: 0.000\\n\\u6b63\\u5728\\u786e\\u4fdd\\u6e38\\u620f\\u51c6\\u5907\\u5c31\\u7eea...\\n\\u6e38\\u620f\\u5df2\\u51c6\\u5907\\u5c31\\u7eea\\n    \\u2705 \\u6570\\u636e\\u6536\\u96c6\\u5b8c\\u6210\\uff0c\\u5171\\u5b8c\\u6210 1 \\u4e2aepisode\\n  \\u2705 \\u8f68\\u8ff9\\u6536\\u96c6\\u5b8c\\u6210\\uff0c\\u8017\\u65f6: 4.07\\u79d2\\n  \\U0001f9e0 \\u66f4\\u65b0\\u667a\\u80fd\\u4f53\\uff083 \\u8f6e\\u8bad\\u7ec3\\uff09...\\n  \\u2705 \\u667a\\u80fd\\u4f53\\u66f4\\u65b0\\u5b8c\\u6210\\uff0c\\u8017\\u65f6: 0.01\\u79d2\\n  \\U0001f4ca \\u8bb0\\u5f55\\u8bad\\u7ec3\\u4fe1\\u606f...\\n\\nEpisode 2 (Total Steps: 40)\\nAverage Reward: 0.00\\nTotal Loss: -0.1156\\nPolicy Loss: -0.0466\\nValue Loss: 0.0000\\nEntropy: 6.9021\\n  \\U0001f3c1 \\u7b2c 3/10 \\u56de\\u5408\\u5b8c\\u6210\\uff0c\\u8017\\u65f6: 4.08\\u79d2\\n  \\U0001f4c8 \\u5df2\\u5b8c\\u6210episode\\u6570: 3\\n  \\U0001f3af \\u6700\\u8fd1\\u5956\\u52b1: 0.0000\\n------------------------------\\n\\U0001f3ae \\u5f00\\u59cb\\u7b2c 4/10 \\u4e2a\\u8bad\\u7ec3\\u56de\\u5408...\\n  \\U0001f4e5 \\u6536\\u96c6\\u8f68\\u8ff9\\u6570\\u636e\\uff0820 \\u6b65\\uff09...\\n\\u6b63\\u5728\\u786e\\u4fdd\\u6e38\\u620f\\u51c6\\u5907\\u5c31\\u7eea...\\n\\u6e38\\u620f\\u5df2\\u51c6\\u5907\\u5c31\\u7eea\\n    \\U0001f504 \\u5f00\\u59cb\\u6536\\u96c6 20 \\u6b65\\u6570\\u636e...\\n    \\U0001f4ca \\u8fdb\\u5ea6: 0/20 \\u6b65\\uff0c\\u5f53\\u524d\\u5956\\u52b1: 0.000\\n    \\U0001f4ca \\u8fdb\\u5ea6: 5/20 \\u6b65\\uff0c\\u5f53\\u524d\\u5956\\u52b1: 0.000\\n    \\U0001f4ca \\u8fdb\\u5ea6: 10/20 \\u6b65\\uff0c\\u5f53\\u524d\\u5956\\u52b1: 0.000\\n    \\U0001f4ca \\u8fdb\\u5ea6: 15/20 \\u6b65\\uff0c\\u5f53\\u524d\\u5956\\u52b1: 0.000\\nEpisode\\u7ed3\\u675f\\uff1a\\u8fbe\\u5230\\u6700\\u5927\\u6b65\\u6570 20\\n    \\U0001f3af Episode 1 \\u5b8c\\u6210\\uff0c\\u5956\\u52b1: 0.000\\n\\u6b63\\u5728\\u786e\\u4fdd\\u6e38\\u620f\\u51c6\\u5907\\u5c31\\u7eea...\\n\\u6e38\\u620f\\u5df2\\u51c6\\u5907\\u5c31\\u7eea\\n    \\u2705 \\u6570\\u636e\\u6536\\u96c6\\u5b8c\\u6210\\uff0c\\u5171\\u5b8c\\u6210 1 \\u4e2aepisode\\n  \\u2705 \\u8f68\\u8ff9\\u6536\\u96c6\\u5b8c\\u6210\\uff0c\\u8017\\u65f6: 4.10\\u79d2\\n  \\U0001f9e0 \\u66f4\\u65b0\\u667a\\u80fd\\u4f53\\uff083 \\u8f6e\\u8bad\\u7ec3\\uff09...\\n  \\u2705 \\u667a\\u80fd\\u4f53\\u66f4\\u65b0\\u5b8c\\u6210\\uff0c\\u8017\\u65f6: 0.01\\u79d2\\n  \\U0001f3af \\u6267\\u884c\\u8bc4\\u4f30...\\n\\u6b63\\u5728\\u786e\\u4fdd\\u6e38\\u620f\\u51c6\\u5907\\u5c31\\u7eea...\\n\\u6e38\\u620f\\u5df2\\u51c6\\u5907\\u5c31\\u7eea\\nEpisode\\u7ed3\\u675f\\uff1a\\u8fbe\\u5230\\u6700\\u5927\\u6b65\\u6570 20\\n\\u6b63\\u5728\\u786e\\u4fdd\\u6e38\\u620f\\u51c6\\u5907\\u5c31\\u7eea...\\n\\u6e38\\u620f\\u5df2\\u51c6\\u5907\\u5c31\\u7eea\\nEpisode\\u7ed3\\u675f\\uff1a\\u8fbe\\u5230\\u6700\\u5927\\u6b65\\u6570 20\\n\\u6b63\\u5728\\u786e\\u4fdd\\u6e38\\u620f\\u51c6\\u5907\\u5c31\\u7eea...\\n\\u6e38\\u620f\\u5df2\\u51c6\\u5907\\u5c31\\u7eea\\nEpisode\\u7ed3\\u675f\\uff1a\\u8fbe\\u5230\\u6700\\u5927\\u6b65\\u6570 20\\n\\u6b63\\u5728\\u786e\\u4fdd\\u6e38\\u620f\\u51c6\\u5907\\u5c31\\u7eea...\\n\\u6e38\\u620f\\u5df2\\u51c6\\u5907\\u5c31\\u7eea\\nEpisode\\u7ed3\\u675f\\uff1a\\u8fbe\\u5230\\u6700\\u5927\\u6b65\\u6570 20\\n\\u6b63\\u5728\\u786e\\u4fdd\\u6e38\\u620f\\u51c6\\u5907\\u5c31\\u7eea...\\n\\u6e38\\u620f\\u5df2\\u51c6\\u5907\\u5c31\\u7eea\\nEpisode\\u7ed3\\u675f\\uff1a\\u8fbe\\u5230\\u6700\\u5927\\u6b65\\u6570 20\\n  \\u2705 \\u8bc4\\u4f30\\u5b8c\\u6210\\uff0c\\u5e73\\u5747\\u5956\\u52b1: 0.0000\\uff0c\\u8017\\u65f6: 20.30\\u79d2\\n  \\U0001f3c1 \\u7b2c 4/10 \\u56de\\u5408\\u5b8c\\u6210\\uff0c\\u8017\\u65f6: 24.42\\u79d2\\n  \\U0001f4c8 \\u5df2\\u5b8c\\u6210episode\\u6570: 4\\n  \\U0001f3af \\u6700\\u8fd1\\u5956\\u52b1: 0.0000\\n------------------------------\\n\\U0001f3ae \\u5f00\\u59cb\\u7b2c 5/10 \\u4e2a\\u8bad\\u7ec3\\u56de\\u5408...\\n  \\U0001f4e5 \\u6536\\u96c6\\u8f68\\u8ff9\\u6570\\u636e\\uff0820 \\u6b65\\uff09...\\n\\u6b63\\u5728\\u786e\\u4fdd\\u6e38\\u620f\\u51c6\\u5907\\u5c31\\u7eea...\\n\\u6e38\\u620f\\u5df2\\u51c6\\u5907\\u5c31\\u7eea\\n    \\U0001f504 \\u5f00\\u59cb\\u6536\\u96c6 20 \\u6b65\\u6570\\u636e...\\n    \\U0001f4ca \\u8fdb\\u5ea6: 0/20 \\u6b65\\uff0c\\u5f53\\u524d\\u5956\\u52b1: 0.000\\n    \\U0001f4ca \\u8fdb\\u5ea6: 5/20 \\u6b65\\uff0c\\u5f53\\u524d\\u5956\\u52b1: 0.000\\n    \\U0001f4ca \\u8fdb\\u5ea6: 10/20 \\u6b65\\uff0c\\u5f53\\u524d\\u5956\\u52b1: 0.000\\n    \\U0001f4ca \\u8fdb\\u5ea6: 15/20 \\u6b65\\uff0c\\u5f53\\u524d\\u5956\\u52b1: 0.000\\nEpisode\\u7ed3\\u675f\\uff1a\\u8fbe\\u5230\\u6700\\u5927\\u6b65\\u6570 20\\n    \\U0001f3af Episode 1 \\u5b8c\\u6210\\uff0c\\u5956\\u52b1: 0.000\\n\\u6b63\\u5728\\u786e\\u4fdd\\u6e38\\u620f\\u51c6\\u5907\\u5c31\\u7eea...\\n\\u6e38\\u620f\\u5df2\\u51c6\\u5907\\u5c31\\u7eea\\n    \\u2705 \\u6570\\u636e\\u6536\\u96c6\\u5b8c\\u6210\\uff0c\\u5171\\u5b8c\\u6210 1 \\u4e2aepisode\\n  \\u2705 \\u8f68\\u8ff9\\u6536\\u96c6\\u5b8c\\u6210\\uff0c\\u8017\\u65f6: 4.12\\u79d2\\n  \\U0001f9e0 \\u66f4\\u65b0\\u667a\\u80fd\\u4f53\\uff083 \\u8f6e\\u8bad\\u7ec3\\uff09...\\n  \\u2705 \\u667a\\u80fd\\u4f53\\u66f4\\u65b0\\u5b8c\\u6210\\uff0c\\u8017\\u65f6: 0.01\\u79d2\\n  \\U0001f4ca \\u8bb0\\u5f55\\u8bad\\u7ec3\\u4fe1\\u606f...\\n\\nEpisode 4 (Total Steps: 80)\\nAverage Reward: 0.00\\nTotal Loss: -0.0312\\nPolicy Loss: 0.0378\\nValue Loss: 0.0000\\nEntropy: 6.9005\\n  \\U0001f3c1 \\u7b2c 5/10 \\u56de\\u5408\\u5b8c\\u6210\\uff0c\\u8017\\u65f6: 4.13\\u79d2\\n  \\U0001f4c8 \\u5df2\\u5b8c\\u6210episode\\u6570: 5\\n  \\U0001f3af \\u6700\\u8fd1\\u5956\\u52b1: 0.0000\\n------------------------------\\n\\U0001f3ae \\u5f00\\u59cb\\u7b2c 6/10 \\u4e2a\\u8bad\\u7ec3\\u56de\\u5408...\\n  \\U0001f4e5 \\u6536\\u96c6\\u8f68\\u8ff9\\u6570\\u636e\\uff0820 \\u6b65\\uff09...\\n\\u6b63\\u5728\\u786e\\u4fdd\\u6e38\\u620f\\u51c6\\u5907\\u5c31\\u7eea...\\n\\u6e38\\u620f\\u5df2\\u51c6\\u5907\\u5c31\\u7eea\\n    \\U0001f504 \\u5f00\\u59cb\\u6536\\u96c6 20 \\u6b65\\u6570\\u636e...\\n    \\U0001f4ca \\u8fdb\\u5ea6: 0/20 \\u6b65\\uff0c\\u5f53\\u524d\\u5956\\u52b1: 0.000\\n    \\U0001f4ca \\u8fdb\\u5ea6: 5/20 \\u6b65\\uff0c\\u5f53\\u524d\\u5956\\u52b1: 0.000\\n    \\U0001f4ca \\u8fdb\\u5ea6: 10/20 \\u6b65\\uff0c\\u5f53\\u524d\\u5956\\u52b1: 0.000\\n    \\U0001f4ca \\u8fdb\\u5ea6: 15/20 \\u6b65\\uff0c\\u5f53\\u524d\\u5956\\u52b1: 0.000\\nEpisode\\u7ed3\\u675f\\uff1a\\u8fbe\\u5230\\u6700\\u5927\\u6b65\\u6570 20\\n    \\U0001f3af Episode 1 \\u5b8c\\u6210\\uff0c\\u5956\\u52b1: 0.000\\n\\u6b63\\u5728\\u786e\\u4fdd\\u6e38\\u620f\\u51c6\\u5907\\u5c31\\u7eea...\\n\\u6e38\\u620f\\u5df2\\u51c6\\u5907\\u5c31\\u7eea\\n    \\u2705 \\u6570\\u636e\\u6536\\u96c6\\u5b8c\\u6210\\uff0c\\u5171\\u5b8c\\u6210 1 \\u4e2aepisode\\n  \\u2705 \\u8f68\\u8ff9\\u6536\\u96c6\\u5b8c\\u6210\\uff0c\\u8017\\u65f6: 4.14\\u79d2\\n  \\U0001f9e0 \\u66f4\\u65b0\\u667a\\u80fd\\u4f53\\uff083 \\u8f6e\\u8bad\\u7ec3\\uff09...\\n  \\u2705 \\u667a\\u80fd\\u4f53\\u66f4\\u65b0\\u5b8c\\u6210\\uff0c\\u8017\\u65f6: 0.01\\u79d2\\n  \\U0001f4be \\u4fdd\\u5b58\\u6a21\\u578b\\u68c0\\u67e5\\u70b9...\\n  \\U0001f3c1 \\u7b2c 6/10 \\u56de\\u5408\\u5b8c\\u6210\\uff0c\\u8017\\u65f6: 4.15\\u79d2\\n  \\U0001f4c8 \\u5df2\\u5b8c\\u6210episode\\u6570: 6\\n  \\U0001f3af \\u6700\\u8fd1\\u5956\\u52b1: 0.0000\\n------------------------------\\n\\U0001f3ae \\u5f00\\u59cb\\u7b2c 7/10 \\u4e2a\\u8bad\\u7ec3\\u56de\\u5408...\\n  \\U0001f4e5 \\u6536\\u96c6\\u8f68\\u8ff9\\u6570\\u636e\\uff0820 \\u6b65\\uff09...\\n\\u6b63\\u5728\\u786e\\u4fdd\\u6e38\\u620f\\u51c6\\u5907\\u5c31\\u7eea...\\n\\u6e38\\u620f\\u5df2\\u51c6\\u5907\\u5c31\\u7eea\\n    \\U0001f504 \\u5f00\\u59cb\\u6536\\u96c6 20 \\u6b65\\u6570\\u636e...\\n    \\U0001f4ca \\u8fdb\\u5ea6: 0/20 \\u6b65\\uff0c\\u5f53\\u524d\\u5956\\u52b1: 0.000\\n    \\U0001f4ca \\u8fdb\\u5ea6: 5/20 \\u6b65\\uff0c\\u5f53\\u524d\\u5956\\u52b1: 0.000\\n    \\U0001f4ca \\u8fdb\\u5ea6: 10/20 \\u6b65\\uff0c\\u5f53\\u524d\\u5956\\u52b1: 0.000\\n    \\U0001f4ca \\u8fdb\\u5ea6: 15/20 \\u6b65\\uff0c\\u5f53\\u524d\\u5956\\u52b1: 0.000\\nEpisode\\u7ed3\\u675f\\uff1a\\u8fbe\\u5230\\u6700\\u5927\\u6b65\\u6570 20\\n    \\U0001f3af Episode 1 \\u5b8c\\u6210\\uff0c\\u5956\\u52b1: 0.000\\n\\u6b63\\u5728\\u786e\\u4fdd\\u6e38\\u620f\\u51c6\\u5907\\u5c31\\u7eea...\\n\\u6e38\\u620f\\u5df2\\u51c6\\u5907\\u5c31\\u7eea\\n    \\u2705 \\u6570\\u636e\\u6536\\u96c6\\u5b8c\\u6210\\uff0c\\u5171\\u5b8c\\u6210 1 \\u4e2aepisode\\n  \\u2705 \\u8f68\\u8ff9\\u6536\\u96c6\\u5b8c\\u6210\\uff0c\\u8017\\u65f6: 4.09\\u79d2\\n  \\U0001f9e0 \\u66f4\\u65b0\\u667a\\u80fd\\u4f53\\uff083 \\u8f6e\\u8bad\\u7ec3\\uff09...\\n  \\u2705 \\u667a\\u80fd\\u4f53\\u66f4\\u65b0\\u5b8c\\u6210\\uff0c\\u8017\\u65f6: 0.01\\u79d2\\n  \\U0001f4ca \\u8bb0\\u5f55\\u8bad\\u7ec3\\u4fe1\\u606f...\\n\\nEpisode 6 (Total Steps: 120)\\nAverage Reward: 0.00\\nTotal Loss: -0.0071\\nPolicy Loss: 0.0619\\nValue Loss: 0.0000\\nEntropy: 6.8936\\n  \\U0001f3af \\u6267\\u884c\\u8bc4\\u4f30...\\n\\u6b63\\u5728\\u786e\\u4fdd\\u6e38\\u620f\\u51c6\\u5907\\u5c31\\u7eea...\\n\\u6e38\\u620f\\u5df2\\u51c6\\u5907\\u5c31\\u7eea\\nEpisode\\u7ed3\\u675f\\uff1a\\u8fbe\\u5230\\u6700\\u5927\\u6b65\\u6570 20\\n\\u6b63\\u5728\\u786e\\u4fdd\\u6e38\\u620f\\u51c6\\u5907\\u5c31\\u7eea...\\n\\u6e38\\u620f\\u5df2\\u51c6\\u5907\\u5c31\\u7eea\\nEpisode\\u7ed3\\u675f\\uff1a\\u8fbe\\u5230\\u6700\\u5927\\u6b65\\u6570 20\\n\\u6b63\\u5728\\u786e\\u4fdd\\u6e38\\u620f\\u51c6\\u5907\\u5c31\\u7eea...\\n\\u6e38\\u620f\\u5df2\\u51c6\\u5907\\u5c31\\u7eea\\nEpisode\\u7ed3\\u675f\\uff1a\\u8fbe\\u5230\\u6700\\u5927\\u6b65\\u6570 20\\n\\u6b63\\u5728\\u786e\\u4fdd\\u6e38\\u620f\\u51c6\\u5907\\u5c31\\u7eea...\\n\\u6e38\\u620f\\u5df2\\u51c6\\u5907\\u5c31\\u7eea\\nEpisode\\u7ed3\\u675f\\uff1a\\u8fbe\\u5230\\u6700\\u5927\\u6b65\\u6570 20\\n\\u6b63\\u5728\\u786e\\u4fdd\\u6e38\\u620f\\u51c6\\u5907\\u5c31\\u7eea...\\n\\u6e38\\u620f\\u5df2\\u51c6\\u5907\\u5c31\\u7eea\\nEpisode\\u7ed3\\u675f\\uff1a\\u8fbe\\u5230\\u6700\\u5927\\u6b65\\u6570 20\\n  \\u2705 \\u8bc4\\u4f30\\u5b8c\\u6210\\uff0c\\u5e73\\u5747\\u5956\\u52b1: 0.0000\\uff0c\\u8017\\u65f6: 20.28\\u79d2\\n  \\U0001f3c1 \\u7b2c 7/10 \\u56de\\u5408\\u5b8c\\u6210\\uff0c\\u8017\\u65f6: 24.38\\u79d2\\n  \\U0001f4c8 \\u5df2\\u5b8c\\u6210episode\\u6570: 7\\n  \\U0001f3af \\u6700\\u8fd1\\u5956\\u52b1: 0.0000\\n------------------------------\\n\\U0001f3ae \\u5f00\\u59cb\\u7b2c 8/10 \\u4e2a\\u8bad\\u7ec3\\u56de\\u5408...\\n  \\U0001f4e5 \\u6536\\u96c6\\u8f68\\u8ff9\\u6570\\u636e\\uff0820 \\u6b65\\uff09...\\n\\u6b63\\u5728\\u786e\\u4fdd\\u6e38\\u620f\\u51c6\\u5907\\u5c31\\u7eea...\\n\\u6e38\\u620f\\u5df2\\u51c6\\u5907\\u5c31\\u7eea\\n    \\U0001f504 \\u5f00\\u59cb\\u6536\\u96c6 20 \\u6b65\\u6570\\u636e...\\n    \\U0001f4ca \\u8fdb\\u5ea6: 0/20 \\u6b65\\uff0c\\u5f53\\u524d\\u5956\\u52b1: 0.000\\n    \\U0001f4ca \\u8fdb\\u5ea6: 5/20 \\u6b65\\uff0c\\u5f53\\u524d\\u5956\\u52b1: 0.000\\n    \\U0001f4ca \\u8fdb\\u5ea6: 10/20 \\u6b65\\uff0c\\u5f53\\u524d\\u5956\\u52b1: 0.000\\n    \\U0001f4ca \\u8fdb\\u5ea6: 15/20 \\u6b65\\uff0c\\u5f53\\u524d\\u5956\\u52b1: 0.000\\nEpisode\\u7ed3\\u675f\\uff1a\\u8fbe\\u5230\\u6700\\u5927\\u6b65\\u6570 20\\n    \\U0001f3af Episode 1 \\u5b8c\\u6210\\uff0c\\u5956\\u52b1: 0.000\\n\\u6b63\\u5728\\u786e\\u4fdd\\u6e38\\u620f\\u51c6\\u5907\\u5c31\\u7eea...\\n\\u6e38\\u620f\\u5df2\\u51c6\\u5907\\u5c31\\u7eea\\n    \\u2705 \\u6570\\u636e\\u6536\\u96c6\\u5b8c\\u6210\\uff0c\\u5171\\u5b8c\\u6210 1 \\u4e2aepisode\\n  \\u2705 \\u8f68\\u8ff9\\u6536\\u96c6\\u5b8c\\u6210\\uff0c\\u8017\\u65f6: 4.10\\u79d2\\n  \\U0001f9e0 \\u66f4\\u65b0\\u667a\\u80fd\\u4f53\\uff083 \\u8f6e\\u8bad\\u7ec3\\uff09...\\n  \\u2705 \\u667a\\u80fd\\u4f53\\u66f4\\u65b0\\u5b8c\\u6210\\uff0c\\u8017\\u65f6: 0.01\\u79d2\\n  \\U0001f3c1 \\u7b2c 8/10 \\u56de\\u5408\\u5b8c\\u6210\\uff0c\\u8017\\u65f6: 4.11\\u79d2\\n  \\U0001f4c8 \\u5df2\\u5b8c\\u6210episode\\u6570: 8\\n  \\U0001f3af \\u6700\\u8fd1\\u5956\\u52b1: 0.0000\\n------------------------------\\n\\U0001f3ae \\u5f00\\u59cb\\u7b2c 9/10 \\u4e2a\\u8bad\\u7ec3\\u56de\\u5408...\\n  \\U0001f4e5 \\u6536\\u96c6\\u8f68\\u8ff9\\u6570\\u636e\\uff0820 \\u6b65\\uff09...\\n\\u6b63\\u5728\\u786e\\u4fdd\\u6e38\\u620f\\u51c6\\u5907\\u5c31\\u7eea...\\n\\u6e38\\u620f\\u5df2\\u51c6\\u5907\\u5c31\\u7eea\\n    \\U0001f504 \\u5f00\\u59cb\\u6536\\u96c6 20 \\u6b65\\u6570\\u636e...\\n    \\U0001f4ca \\u8fdb\\u5ea6: 0/20 \\u6b65\\uff0c\\u5f53\\u524d\\u5956\\u52b1: 0.000\\n    \\U0001f4ca \\u8fdb\\u5ea6: 5/20 \\u6b65\\uff0c\\u5f53\\u524d\\u5956\\u52b1: 0.000\\n    \\U0001f4ca \\u8fdb\\u5ea6: 10/20 \\u6b65\\uff0c\\u5f53\\u524d\\u5956\\u52b1: 0.000\\n    \\U0001f4ca \\u8fdb\\u5ea6: 15/20 \\u6b65\\uff0c\\u5f53\\u524d\\u5956\\u52b1: 0.000\\nEpisode\\u7ed3\\u675f\\uff1a\\u8fbe\\u5230\\u6700\\u5927\\u6b65\\u6570 20\\n    \\U0001f3af Episode 1 \\u5b8c\\u6210\\uff0c\\u5956\\u52b1: 0.000\\n\\u6b63\\u5728\\u786e\\u4fdd\\u6e38\\u620f\\u51c6\\u5907\\u5c31\\u7eea...\\n\\u6e38\\u620f\\u5df2\\u51c6\\u5907\\u5c31\\u7eea\\n    \\u2705 \\u6570\\u636e\\u6536\\u96c6\\u5b8c\\u6210\\uff0c\\u5171\\u5b8c\\u6210 1 \\u4e2aepisode\\n  \\u2705 \\u8f68\\u8ff9\\u6536\\u96c6\\u5b8c\\u6210\\uff0c\\u8017\\u65f6: 4.08\\u79d2\\n  \\U0001f9e0 \\u66f4\\u65b0\\u667a\\u80fd\\u4f53\\uff083 \\u8f6e\\u8bad\\u7ec3\\uff09...\\n  \\u2705 \\u667a\\u80fd\\u4f53\\u66f4\\u65b0\\u5b8c\\u6210\\uff0c\\u8017\\u65f6: 0.01\\u79d2\\n  \\U0001f4ca \\u8bb0\\u5f55\\u8bad\\u7ec3\\u4fe1\\u606f...\\n\\nEpisode 8 (Total Steps: 160)\\nAverage Reward: 0.00\\nTotal Loss: -0.0625\\nPolicy Loss: 0.0063\\nValue Loss: 0.0000\\nEntropy: 6.8729\\n  \\U0001f3c1 \\u7b2c 9/10 \\u56de\\u5408\\u5b8c\\u6210\\uff0c\\u8017\\u65f6: 4.10\\u79d2\\n  \\U0001f4c8 \\u5df2\\u5b8c\\u6210episode\\u6570: 9\\n  \\U0001f3af \\u6700\\u8fd1\\u5956\\u52b1: 0.0000\\n------------------------------\\n\\U0001f3ae \\u5f00\\u59cb\\u7b2c 10/10 \\u4e2a\\u8bad\\u7ec3\\u56de\\u5408...\\n  \\U0001f4e5 \\u6536\\u96c6\\u8f68\\u8ff9\\u6570\\u636e\\uff0820 \\u6b65\\uff09...\\n\\u6b63\\u5728\\u786e\\u4fdd\\u6e38\\u620f\\u51c6\\u5907\\u5c31\\u7eea...\\n\\u6e38\\u620f\\u5df2\\u51c6\\u5907\\u5c31\\u7eea\\n    \\U0001f504 \\u5f00\\u59cb\\u6536\\u96c6 20 \\u6b65\\u6570\\u636e...\\n    \\U0001f4ca \\u8fdb\\u5ea6: 0/20 \\u6b65\\uff0c\\u5f53\\u524d\\u5956\\u52b1: 0.000\\n    \\U0001f4ca \\u8fdb\\u5ea6: 5/20 \\u6b65\\uff0c\\u5f53\\u524d\\u5956\\u52b1: 0.000\\n    \\U0001f4ca \\u8fdb\\u5ea6: 10/20 \\u6b65\\uff0c\\u5f53\\u524d\\u5956\\u52b1: 0.000\\n    \\U0001f4ca \\u8fdb\\u5ea6: 15/20 \\u6b65\\uff0c\\u5f53\\u524d\\u5956\\u52b1: 0.000\\nEpisode\\u7ed3\\u675f\\uff1a\\u8fbe\\u5230\\u6700\\u5927\\u6b65\\u6570 20\\n    \\U0001f3af Episode 1 \\u5b8c\\u6210\\uff0c\\u5956\\u52b1: 0.000\\n\\u6b63\\u5728\\u786e\\u4fdd\\u6e38\\u620f\\u51c6\\u5907\\u5c31\\u7eea...\\n\\u6e38\\u620f\\u5df2\\u51c6\\u5907\\u5c31\\u7eea\\n    \\u2705 \\u6570\\u636e\\u6536\\u96c6\\u5b8c\\u6210\\uff0c\\u5171\\u5b8c\\u6210 1 \\u4e2aepisode\\n  \\u2705 \\u8f68\\u8ff9\\u6536\\u96c6\\u5b8c\\u6210\\uff0c\\u8017\\u65f6: 4.13\\u79d2\\n  \\U0001f9e0 \\u66f4\\u65b0\\u667a\\u80fd\\u4f53\\uff083 \\u8f6e\\u8bad\\u7ec3\\uff09...\\n  \\u2705 \\u667a\\u80fd\\u4f53\\u66f4\\u65b0\\u5b8c\\u6210\\uff0c\\u8017\\u65f6: 0.01\\u79d2\\n  \\U0001f3af \\u6267\\u884c\\u8bc4\\u4f30...\\n\\u6b63\\u5728\\u786e\\u4fdd\\u6e38\\u620f\\u51c6\\u5907\\u5c31\\u7eea...\\n\\u6e38\\u620f\\u5df2\\u51c6\\u5907\\u5c31\\u7eea\\nEpisode\\u7ed3\\u675f\\uff1a\\u8fbe\\u5230\\u6700\\u5927\\u6b65\\u6570 20\\n\\u6b63\\u5728\\u786e\\u4fdd\\u6e38\\u620f\\u51c6\\u5907\\u5c31\\u7eea...\\n\\u6e38\\u620f\\u5df2\\u51c6\\u5907\\u5c31\\u7eea\\nEpisode\\u7ed3\\u675f\\uff1a\\u8fbe\\u5230\\u6700\\u5927\\u6b65\\u6570 20\\n\\u6b63\\u5728\\u786e\\u4fdd\\u6e38\\u620f\\u51c6\\u5907\\u5c31\\u7eea...\\n\\u6e38\\u620f\\u5df2\\u51c6\\u5907\\u5c31\\u7eea\\nEpisode\\u7ed3\\u675f\\uff1a\\u8fbe\\u5230\\u6700\\u5927\\u6b65\\u6570 20\\n\\u6b63\\u5728\\u786e\\u4fdd\\u6e38\\u620f\\u51c6\\u5907\\u5c31\\u7eea...\\n\\u6e38\\u620f\\u5df2\\u51c6\\u5907\\u5c31\\u7eea\\nEpisode\\u7ed3\\u675f\\uff1a\\u8fbe\\u5230\\u6700\\u5927\\u6b65\\u6570 20\\n\\u6b63\\u5728\\u786e\\u4fdd\\u6e38\\u620f\\u51c6\\u5907\\u5c31\\u7eea...\\n\\u6e38\\u620f\\u5df2\\u51c6\\u5907\\u5c31\\u7eea\\nEpisode\\u7ed3\\u675f\\uff1a\\u8fbe\\u5230\\u6700\\u5927\\u6b65\\u6570 20\\n  \\u2705 \\u8bc4\\u4f30\\u5b8c\\u6210\\uff0c\\u5e73\\u5747\\u5956\\u52b1: 0.0000\\uff0c\\u8017\\u65f6: 20.28\\u79d2\\n  \\U0001f3c1 \\u7b2c 10/10 \\u56de\\u5408\\u5b8c\\u6210\\uff0c\\u8017\\u65f6: 24.42\\u79d2\\n  \\U0001f4c8 \\u5df2\\u5b8c\\u6210episode\\u6570: 10\\n  \\U0001f3af \\u6700\\u8fd1\\u5956\\u52b1: 0.0000\\n------------------------------\\n\\n\\u8bad\\u7ec3\\u5b8c\\u6210! \\u603b\\u7528\\u65f6: 122.35\\u79d2\\n\\u6700\\u4f73\\u8bc4\\u4f30\\u5956\\u52b1: 0.00\n---------------------------- Captured stderr call -----------------------------\n2025-06-26 09:42:04,901 - torch_game.tests.integration.test_training_pipeline - INFO - \\ufffd\\ufffd\\u02bc\\ufffd\\ufffd\\ufffd\\ufffd\\ufffd\\ufffd\\u0579\\u0475\\ufffd\\ufffd...\\r\n------------------------------ Captured log call ------------------------------\nINFO     torch_game.tests.integration.test_training_pipeline:test_training_pipeline.py:436 开始测试扩展训练...\n- generated xml file: E:\\xy2_yolo_auxiliary\\torch_game\\test_reports\\integration_tests.xml -\n=========================== short test summary info ===========================\nFAILED tests\\integration\\test_training_pipeline.py::TestTrainingPipeline::test_extended_training\n=========== 1 failed, 13 passed, 10 deselected in 452.36s (0:07:32) ===========", "stderr": "", "summary": "=========== 1 failed, 13 passed, 10 deselected in 452.36s (0:07:32) ===========", "success": false}, "performance": {"test_type": "performance_tests", "return_code": 1, "duration": 14.422349452972412, "stdout": "============================= test session starts =============================\nplatform win32 -- Python 3.10.10, pytest-8.4.1, pluggy-1.6.0\nbenchmark: 5.1.0 (defaults: timer=time.perf_counter disable_gc=False min_rounds=5 min_time=0.000005 max_time=1.0 calibration_precision=10 warmup=False warmup_iterations=100000)\nrootdir: E:\\xy2_yolo_auxiliary\nconfigfile: pytest.ini\nplugins: anyio-4.8.0, benchmark-5.1.0, cov-6.2.1, html-4.1.1, metadata-3.1.1\ncollected 11 items\n\ntests\\performance\\test_memory_profiling.py ..F..                         [ 45%]\ntests\\performance\\test_performance_benchmarks.py ......                  [100%]\n\n================================== FAILURES ===================================\n_______________ TestMemoryProfiling.test_training_memory_growth _______________\ntests\\performance\\test_memory_profiling.py:220: in test_training_memory_growth\n    self.trainer = Trainer(\nE   TypeError: Trainer.__init__() got an unexpected keyword argument 'n_episodes'\n---------------------------- Captured stderr setup ----------------------------\n2025-06-26 09:44:15,822 - torch_game.tests.performance.test_memory_profiling - INFO - \\ufffd\\ufffd\\ufffd\\u053f\\ufffd\\u02bc\\u02b1\\ufffd\\u06b4\\ufffd\\u02b9\\ufffd\\ufffd: {'rss_mb': 268.64453125, 'vms_mb': 3679.55078125, 'percent': 0.8266913617466192}\\r\n----------------------------- Captured log setup ------------------------------\nINFO     torch_game.tests.performance.test_memory_profiling:test_memory_profiling.py:58 测试开始时内存使用: {'rss_mb': 268.64453125, 'vms_mb': 3679.55078125, 'percent': 0.8266913617466192}\n---------------------------- Captured stderr call -----------------------------\n2025-06-26 09:44:15,822 - torch_game.tests.performance.test_memory_profiling - INFO - \\ufffd\\ufffd\\u02bc\\ufffd\\ufffd\\ufffd\\ufffd\\u0475\\ufffd\\ufffd\\ufffd\\u06b4\\ufffd\\ufffd\\ufffd\\ufffd\\ufffd...\\r\n------------------------------ Captured log call ------------------------------\nINFO     torch_game.tests.performance.test_memory_profiling:test_memory_profiling.py:214 开始测试训练内存增长...\n-------------------------- Captured stderr teardown ---------------------------\n2025-06-26 09:44:16,173 - torch_game.tests.performance.test_memory_profiling - INFO - \\ufffd\\ufffd\\ufffd\\u053d\\ufffd\\ufffd\\ufffd\\u02b1\\ufffd\\u06b4\\ufffd\\u02b9\\ufffd\\ufffd: {'rss_mb': 269.1875, 'vms_mb': 3679.55078125, 'percent': 0.8283622223937158}\\r\\n2025-06-26 09:44:16,173 - torch_game.tests.performance.test_memory_profiling - INFO - \\ufffd\\u06b4\\ufffd\\u4eef: 0.54MB\\r\n---------------------------- Captured log teardown ----------------------------\nINFO     torch_game.tests.performance.test_memory_profiling:test_memory_profiling.py:85 测试结束时内存使用: {'rss_mb': 269.1875, 'vms_mb': 3679.55078125, 'percent': 0.8283622223937158}\nINFO     torch_game.tests.performance.test_memory_profiling:test_memory_profiling.py:86 内存变化: 0.54MB\n- generated xml file: E:\\xy2_yolo_auxiliary\\torch_game\\test_reports\\performance_tests.xml -\n=========================== short test summary info ===========================\nFAILED tests\\performance\\test_memory_profiling.py::TestMemoryProfiling::test_training_memory_growth\n======================== 1 failed, 10 passed in 10.40s ========================", "stderr": "", "summary": "======================== 1 failed, 10 passed in 10.40s ========================", "success": false}}